# Page snapshot

```yaml
- application "Vocab Learning Application":
  - main:
    - img
    - text: Admin Panel
    - navigation:
      - button "Dashboard Overview and statistics":
        - img
        - text: Dashboard Overview and statistics
      - button "Users Manage user accounts":
        - img
        - text: Users Manage user accounts
      - button "Collections Manage collections":
        - img
        - text: Collections Manage collections
      - button "Feedback User feedback management":
        - img
        - text: Feedback User feedback management
      - button "System System monitoring":
        - img
        - text: System System monitoring
      - button "Audit Logs Security audit trail":
        - img
        - text: Audit Logs Security audit trail
      - button "Settings Admin settings":
        - img
        - text: Settings Admin settings
    - button "Logout":
      - img
      - text: Logout
    - text: Admin Dashboard
    - main:
      - heading "User Management" [level=1]
      - paragraph: Manage user accounts and permissions
      - button "Refresh":
        - img
        - text: Refresh
      - region:
        - group:
          - textbox "Search users..."
          - button "Search":
            - img
            - text: Search
      - region:
        - group:
          - heading "Users (8)" [level=3]:
            - img
            - text: Users (8)
        - group:
          - table:
            - rowgroup:
              - row "User Provider Role Status Created Actions":
                - cell "User"
                - cell "Provider"
                - cell "Role"
                - cell "Status"
                - cell "Created"
                - cell "Actions"
            - rowgroup:
              - row "invalid USERNAME_PASSWORD Disabled 7/21/2025":
                - cell "invalid":
                  - img
                  - text: invalid
                - cell "USERNAME_PASSWORD"
                - cell:
                  - combobox: User
                - cell "Disabled"
                - cell "7/21/2025"
                - cell:
                  - button:
                    - img
              - row "testuser3 USERNAME_PASSWORD Active 7/21/2025":
                - cell "testuser3":
                  - img
                  - text: testuser3
                - cell "USERNAME_PASSWORD"
                - cell:
                  - combobox: User
                - cell "Active"
                - cell "7/21/2025"
                - cell:
                  - button:
                    - img
              - row "testuser2 USERNAME_PASSWORD Active 7/21/2025":
                - cell "testuser2":
                  - img
                  - text: testuser2
                - cell "USERNAME_PASSWORD"
                - cell:
                  - combobox: User
                - cell "Active"
                - cell "7/21/2025"
                - cell:
                  - button:
                    - img
              - row "testuser1 USERNAME_PASSWORD Active 7/21/2025":
                - cell "testuser1":
                  - img
                  - text: testuser1
                - cell "USERNAME_PASSWORD"
                - cell:
                  - combobox: User
                - cell "Active"
                - cell "7/21/2025"
                - cell:
                  - button:
                    - img
              - row "superadmin USERNAME_PASSWORD Active 7/21/2025":
                - cell "superadmin":
                  - img
                  - text: superadmin
                - cell "USERNAME_PASSWORD"
                - cell:
                  - combobox: Admin
                - cell "Active"
                - cell "7/21/2025"
                - cell:
                  - button:
                    - img
              - row "admin USERNAME_PASSWORD Active 7/21/2025":
                - cell "admin":
                  - img
                  - text: admin
                - cell "USERNAME_PASSWORD"
                - cell:
                  - combobox: Admin
                - cell "Active"
                - cell "7/21/2025"
                - cell:
                  - button:
                    - img
              - row "testuser USERNAME_PASSWORD Active 7/5/2025":
                - cell "testuser":
                  - img
                  - text: testuser
                - cell "USERNAME_PASSWORD"
                - cell:
                  - combobox: User
                - cell "Active"
                - cell "7/5/2025"
                - cell:
                  - button:
                    - img
              - row "CT030343 USERNAME_PASSWORD Active 7/5/2025":
                - cell "CT030343":
                  - img
                  - text: CT030343
                - cell "USERNAME_PASSWORD"
                - cell:
                  - combobox: User
                - cell "Active"
                - cell "7/5/2025"
                - cell:
                  - button:
                    - img
  - button "Open settings":
    - img
  - button "Send us your feedback":
    - img
  - region "Notifications alt+T":
    - list:
      - listitem:
        - img
        - text: Login successful
  - alert
```