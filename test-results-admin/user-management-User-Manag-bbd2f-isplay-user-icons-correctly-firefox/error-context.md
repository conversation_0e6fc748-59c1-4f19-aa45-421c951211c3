# Page snapshot

```yaml
- application "Vocab Learning Application":
  - main:
    - img
    - text: Admin Panel
    - navigation:
      - button "Dashboard Overview and statistics":
        - img
        - text: Dashboard Overview and statistics
      - button "Users Manage user accounts":
        - img
        - text: Users Manage user accounts
      - button "Collections Manage collections":
        - img
        - text: Collections Manage collections
      - button "Feedback User feedback management":
        - img
        - text: Feedback User feedback management
      - button "System System monitoring":
        - img
        - text: System System monitoring
      - button "Audit Logs Security audit trail":
        - img
        - text: Audit Logs Security audit trail
      - button "Settings Admin settings":
        - img
        - text: Settings Admin settings
    - button "Logout":
      - img
      - text: Logout
    - text: Admin Dashboard
    - main:
      - heading "User Management" [level=1]
      - paragraph: Manage user accounts and permissions
      - button "Refresh":
        - img
        - text: Refresh
      - region:
        - group:
          - textbox "Search users..."
          - button "Search":
            - img
            - text: Search
      - region:
        - group:
          - heading "Users (0)" [level=3]:
            - img
            - text: Users (0)
        - group:
          - table:
            - rowgroup:
              - row "User Provider Role Status Created Actions":
                - cell "User"
                - cell "Provider"
                - cell "Role"
                - cell "Status"
                - cell "Created"
                - cell "Actions"
            - rowgroup
  - button "Open settings":
    - img
  - button "Send us your feedback":
    - img
  - region "Notifications alt+T"
  - alert
```