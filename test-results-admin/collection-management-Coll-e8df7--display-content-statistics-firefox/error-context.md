# Page snapshot

```yaml
- application "Vocab Learning Application":
  - main:
    - img
    - text: Admin Panel
    - navigation:
      - button "Dashboard Overview and statistics":
        - img
        - text: Dashboard Overview and statistics
      - button "Users Manage user accounts":
        - img
        - text: Users Manage user accounts
      - button "Collections Manage collections":
        - img
        - text: Collections Manage collections
      - button "Feedback User feedback management":
        - img
        - text: Feedback User feedback management
      - button "System System monitoring":
        - img
        - text: System System monitoring
      - button "Audit Logs Security audit trail":
        - img
        - text: Audit Logs Security audit trail
      - button "Settings Admin settings":
        - img
        - text: Settings Admin settings
    - button "Logout":
      - img
      - text: Logout
    - text: Admin Dashboard
    - main:
      - heading "Collection Management" [level=1]
      - paragraph: Manage user collections and content
      - button "Refresh":
        - img
        - text: Refresh
      - region:
        - group:
          - heading "Test Collection" [level=3]:
            - img
            - text: Test Collection
            - button:
              - img
            - button:
              - img
        - group:
          - text: VI → EN 0 Words 0 Paragraphs 0 Keywords
          - img
          - text: "User ID: 4f089785..."
          - img
          - text: "Created: 7/18/2025"
      - region:
        - group:
          - heading "Col 1" [level=3]:
            - img
            - text: Col 1
            - button:
              - img
            - button:
              - img
        - group:
          - text: VI → EN 9 Words 0 Paragraphs 0 Keywords
          - img
          - text: "User ID: 92c2d195..."
          - img
          - text: "Created: 7/5/2025"
  - button "Open settings":
    - img
  - button "Send us your feedback":
    - img
  - region "Notifications alt+T":
    - list:
      - listitem:
        - img
        - text: "Failed to load dashboard data Error: Error"
  - alert
```