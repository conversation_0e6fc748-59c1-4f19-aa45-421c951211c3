# Page snapshot

```yaml
- application "Vocab Learning Application":
  - main:
    - img
    - text: Admin Panel
    - navigation:
      - button "Dashboard Overview and statistics":
        - img
        - text: Dashboard Overview and statistics
      - button "Users Manage user accounts":
        - img
        - text: Users Manage user accounts
      - button "Collections Manage collections":
        - img
        - text: Collections Manage collections
      - button "Feedback User feedback management":
        - img
        - text: Feedback User feedback management
      - button "System System monitoring":
        - img
        - text: System System monitoring
      - button "Audit Logs Security audit trail":
        - img
        - text: Audit Logs Security audit trail
      - button "Settings Admin settings":
        - img
        - text: Settings Admin settings
    - button "Logout":
      - img
      - text: Logout
    - text: Admin Dashboard
    - main:
      - heading "Feedback Dashboard" [level=1]
      - button "Refresh":
        - img
        - text: Refresh
      - region:
        - group:
          - img
          - textbox "Search feedbacks..."
          - img
          - combobox: All Status
      - region:
        - group:
          - text: "4"
          - paragraph: Total Feedbacks
      - region:
        - group:
          - text: "4"
          - paragraph: Pending
      - region:
        - group:
          - text: "0"
          - paragraph: Reviewed
      - region:
        - group:
          - text: "0"
          - paragraph: Resolved
      - region:
        - group:
          - text: pending 7/21/2025, 2:49:31 AM
          - strong: "User:"
          - text: "CT030343(USERNAME_PASSWORD: CT030343)"
          - combobox: Pending
          - textbox: asdasdsad
      - region:
        - group:
          - text: pending 7/21/2025, 2:49:24 AM
          - strong: "User:"
          - text: "CT030343(USERNAME_PASSWORD: CT030343)"
          - combobox: Pending
          - textbox: asdasd
      - region:
        - group:
          - text: pending 7/21/2025, 1:52:35 AM
          - strong: "User:"
          - text: "CT030343(USERNAME_PASSWORD: CT030343)"
          - combobox: Pending
          - textbox: asdasd
      - region:
        - group:
          - text: pending 7/20/2025, 3:23:24 AM
          - strong: "User:"
          - text: "CT030343(USERNAME_PASSWORD: CT030343)"
          - combobox: Pending
          - textbox: asdsadd
  - button "Open settings":
    - img
  - button "Send us your feedback":
    - img
  - region "Notifications alt+T":
    - list:
      - listitem:
        - img
        - text: Login successful
  - alert
```