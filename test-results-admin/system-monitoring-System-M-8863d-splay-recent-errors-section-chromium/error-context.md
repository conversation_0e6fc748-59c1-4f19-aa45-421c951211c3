# Page snapshot

```yaml
- application "Vocab Learning Application":
  - main:
    - img
    - text: Admin Panel
    - navigation:
      - button "Dashboard Overview and statistics":
        - img
        - text: Dashboard Overview and statistics
      - button "Users Manage user accounts":
        - img
        - text: Users Manage user accounts
      - button "Collections Manage collections":
        - img
        - text: Collections Manage collections
      - button "Feedback User feedback management":
        - img
        - text: Feedback User feedback management
      - button "System System monitoring":
        - img
        - text: System System monitoring
      - button "Audit Logs Security audit trail":
        - img
        - text: Audit Logs Security audit trail
      - button "Settings Admin settings":
        - img
        - text: Settings Admin settings
    - button "Logout":
      - img
      - text: Logout
    - text: Admin Dashboard
    - main:
      - heading "System Monitoring" [level=1]
      - paragraph: Monitor system health and performance
      - button "Clear Cache":
        - img
        - text: Clear Cache
      - button "Refresh":
        - img
        - text: Refresh
      - region:
        - group:
          - heading "System Health" [level=3]:
            - img
            - text: System Health
        - group:
          - img
          - text: HEALTHY
          - img
          - text: Database connected
          - img
          - text: Cache operational
          - img
          - text: AI Services operational
      - region:
        - group:
          - heading "AI Token Usage" [level=3]:
            - img
            - text: AI Token Usage
        - group: 0 Total Tokens $0.00 Total Cost 0 API Requests 0 Avg Tokens/Request
      - region:
        - group:
          - heading "Recent Errors" [level=3]:
            - img
            - text: Recent Errors
        - group:
          - img
          - paragraph: No recent errors found
  - button "Open settings":
    - img
  - button "Send us your feedback":
    - img
  - region "Notifications alt+T":
    - list:
      - listitem:
        - img
        - text: Login successful
  - alert: Vocab
```