# Page snapshot

```yaml
- application "Vocab Learning Application":
  - main:
    - img
    - text: Admin Panel
    - navigation:
      - button "Dashboard Overview and statistics":
        - img
        - text: Dashboard Overview and statistics
      - button "Users Manage user accounts":
        - img
        - text: Users Manage user accounts
      - button "Collections Manage collections":
        - img
        - text: Collections Manage collections
      - button "Feedback User feedback management":
        - img
        - text: Feedback User feedback management
      - button "System System monitoring":
        - img
        - text: System System monitoring
      - button "Audit Logs Security audit trail":
        - img
        - text: Audit Logs Security audit trail
      - button "Settings Admin settings":
        - img
        - text: Settings Admin settings
    - button "Logout":
      - img
      - text: Logout
    - text: Admin Dashboard
    - main:
      - heading "Admin Dashboard" [level=1]
      - paragraph: System overview and key metrics
      - button "Refresh":
        - img
        - text: Refresh
      - region:
        - group:
          - heading "System Health" [level=3]:
            - img
            - text: System Health
        - group: HEALTHY Database:connected Cache:operational AI Services:operational
      - region:
        - group:
          - heading "Total Users" [level=3]
          - img
        - group: "9 +7 new this week Active: 8 | Admins: 2"
      - region:
        - group:
          - heading "Collections" [level=3]
          - img
        - group: "2 Public: 0 | Private: 2"
      - region:
        - group:
          - heading "Feedback" [level=3]
          - img
        - group: 4 4 pending 0 resolved
      - region:
        - group:
          - heading "Performance" [level=3]
          - img
        - group: "150ms Cache Hit: 85.0% Error Rate: 2.00%"
  - button "Open settings":
    - img
  - button "Send us your feedback":
    - img
  - region "Notifications alt+T"
  - alert
```