import { test, expect } from '@playwright/test';

test.describe('Feedback Management', () => {
	test.beforeEach(async ({ page }) => {
		// Login as admin and navigate to feedback management
		await page.goto('/admin/login');
		await page.fill('input[id="username"]', 'admin');
		await page.fill('input[id="password"]', 'admin123');
		await page.click('button[type="submit"]');
		await page.waitForURL('/admin');
		
		// Navigate to feedback management
		await page.click('text=Feedback');
		await page.waitForURL('/admin/feedback');
	});

	test('should display feedback management page correctly', async ({ page }) => {
		// Check page title (should be in admin layout)
		await expect(page.locator('h1, h2').filter({ hasText: 'Feedback' })).toBeVisible();
		
		// Check for feedback-related elements
		await expect(page.locator('text=Status').or(page.locator('text=Message'))).toBeVisible();
	});

	test('should display feedback list', async ({ page }) => {
		// Wait for feedback data to load
		await page.waitForTimeout(2000);
		
		// Check if feedback items are displayed or empty state
		const hasFeedback = await page.locator('text=pending').or(page.locator('text=resolved')).isVisible();
		const hasEmptyState = await page.locator('text=No feedback').or(page.locator('text=Empty')).isVisible();
		
		expect(hasFeedback || hasEmptyState).toBe(true);
	});

	test('should display feedback status badges', async ({ page }) => {
		// Wait for feedback data to load
		await page.waitForTimeout(2000);
		
		// Look for status badges
		const statusBadges = page.locator('[class*="badge"], [class*="bg-"]').filter({ 
			hasText: /pending|resolved|reviewed/ 
		});
		
		const badgeCount = await statusBadges.count();
		
		if (badgeCount > 0) {
			// Check that status badges are visible
			await expect(statusBadges.first()).toBeVisible();
		}
	});

	test('should handle feedback status updates', async ({ page }) => {
		// Wait for feedback data to load
		await page.waitForTimeout(2000);
		
		// Look for feedback items with status dropdowns
		const statusSelects = page.locator('select, [role="combobox"]').filter({ 
			hasText: /pending|resolved|reviewed/ 
		});
		
		const selectCount = await statusSelects.count();
		
		if (selectCount > 0) {
			const firstSelect = statusSelects.first();
			
			// Get current status
			const currentStatus = await firstSelect.textContent();
			
			// Click to open dropdown
			await firstSelect.click();
			
			// Select different status
			if (currentStatus?.includes('pending')) {
				const resolvedOption = page.locator('text=resolved').or(page.locator('text=reviewed'));
				if (await resolvedOption.isVisible()) {
					await resolvedOption.click();
				}
			} else {
				const pendingOption = page.locator('text=pending');
				if (await pendingOption.isVisible()) {
					await pendingOption.click();
				}
			}
			
			// Wait for update to complete
			await page.waitForTimeout(1000);
			
			// Should show success message or updated status
			await expect(page.locator('text=updated').or(page.locator('text=success'))).toBeVisible();
		}
	});

	test('should display feedback messages', async ({ page }) => {
		// Wait for feedback data to load
		await page.waitForTimeout(2000);
		
		// Look for feedback message content
		const feedbackMessages = page.locator('textarea, [class*="text-"], p').filter({ 
			hasText: /feedback|message|comment/ 
		});
		
		const messageCount = await feedbackMessages.count();
		
		if (messageCount > 0) {
			await expect(feedbackMessages.first()).toBeVisible();
		} else {
			// Check for sample feedback from seeding
			const sampleMessage = page.locator('text=sample feedback').or(page.locator('text=testing'));
			if (await sampleMessage.isVisible()) {
				await expect(sampleMessage).toBeVisible();
			}
		}
	});

	test('should handle feedback filtering', async ({ page }) => {
		// Wait for feedback data to load
		await page.waitForTimeout(2000);
		
		// Look for filter controls
		const filterControls = page.locator('select, input').filter({ 
			hasText: /filter|status|search/ 
		});
		
		const filterCount = await filterControls.count();
		
		if (filterCount > 0) {
			const firstFilter = filterControls.first();
			
			// Try to use filter
			if (await firstFilter.getAttribute('type') === 'text') {
				// Text input - try searching
				await firstFilter.fill('test');
				await page.keyboard.press('Enter');
			} else {
				// Select dropdown - try filtering
				await firstFilter.click();
				
				const filterOption = page.locator('option, [role="option"]').first();
				if (await filterOption.isVisible()) {
					await filterOption.click();
				}
			}
			
			// Wait for filter to apply
			await page.waitForTimeout(1000);
		}
	});

	test('should display feedback timestamps', async ({ page }) => {
		// Wait for feedback data to load
		await page.waitForTimeout(2000);
		
		// Look for timestamp information
		const timestamps = page.locator('text=/\\d{1,2}\/\\d{1,2}\/\\d{4}|\\d{4}-\\d{2}-\\d{2}|ago|Created/');
		
		const timestampCount = await timestamps.count();
		
		if (timestampCount > 0) {
			await expect(timestamps.first()).toBeVisible();
		}
	});

	test('should handle refresh functionality', async ({ page }) => {
		// Wait for initial load
		await page.waitForTimeout(2000);
		
		// Look for refresh button
		const refreshButton = page.locator('button').filter({ hasText: 'Refresh' });
		
		if (await refreshButton.isVisible()) {
			await refreshButton.click();
			
			// Wait for refresh to complete
			await page.waitForTimeout(1000);
			
			// Page should still be functional
			await expect(page.locator('h1, h2').filter({ hasText: 'Feedback' })).toBeVisible();
		}
	});

	test('should display user information for feedback', async ({ page }) => {
		// Wait for feedback data to load
		await page.waitForTimeout(2000);
		
		// Look for user information
		const userInfo = page.locator('text=User').or(page.locator('text=From:'));
		
		if (await userInfo.isVisible()) {
			await expect(userInfo).toBeVisible();
		}
	});

	test('should handle pagination if available', async ({ page }) => {
		// Wait for feedback data to load
		await page.waitForTimeout(2000);
		
		// Check if pagination exists
		const nextButton = page.locator('button').filter({ hasText: 'Next' });
		const prevButton = page.locator('button').filter({ hasText: 'Previous' });
		
		if (await nextButton.isVisible()) {
			// Test pagination
			await nextButton.click();
			await page.waitForTimeout(1000);
			
			// Should still show feedback page
			await expect(page.locator('h1, h2').filter({ hasText: 'Feedback' })).toBeVisible();
			
			// Test previous button
			if (await prevButton.isVisible()) {
				await prevButton.click();
				await page.waitForTimeout(1000);
				await expect(page.locator('h1, h2').filter({ hasText: 'Feedback' })).toBeVisible();
			}
		}
	});

	test('should display feedback count information', async ({ page }) => {
		// Wait for feedback data to load
		await page.waitForTimeout(2000);
		
		// Look for feedback count
		const countInfo = page.locator('text=/\\d+ feedback|Total:|Showing/');
		
		if (await countInfo.isVisible()) {
			await expect(countInfo).toBeVisible();
		}
	});

	test('should handle empty feedback state', async ({ page }) => {
		// Wait for feedback data to load
		await page.waitForTimeout(2000);
		
		// Check if empty state is shown
		const emptyState = page.locator('text=No feedback').or(page.locator('text=Empty'));
		const hasFeedback = await page.locator('text=pending').or(page.locator('text=resolved')).isVisible();
		
		if (!hasFeedback) {
			// Should show empty state message
			if (await emptyState.isVisible()) {
				await expect(emptyState).toBeVisible();
			}
		}
	});

	test('should display feedback actions', async ({ page }) => {
		// Wait for feedback data to load
		await page.waitForTimeout(2000);
		
		// Look for action buttons or controls
		const actionButtons = page.locator('button').filter({ 
			hasText: /update|edit|delete|resolve|review/ 
		});
		
		const buttonCount = await actionButtons.count();
		
		if (buttonCount > 0) {
			await expect(actionButtons.first()).toBeVisible();
		}
	});

	test('should handle loading states', async ({ page }) => {
		// Check that page loads without showing loading spinner indefinitely
		await page.waitForTimeout(3000);
		
		// Loading spinner should not be visible after data loads
		const loadingSpinner = page.locator('[class*="animate-spin"]');
		if (await loadingSpinner.isVisible()) {
			// Wait for loading to complete
			await loadingSpinner.waitFor({ state: 'hidden', timeout: 5000 });
		}
		
		// Page content should be visible
		await expect(page.locator('h1, h2').filter({ hasText: 'Feedback' })).toBeVisible();
	});

	test('should maintain responsive layout', async ({ page }) => {
		// Test desktop layout
		await page.setViewportSize({ width: 1200, height: 800 });
		await page.waitForTimeout(1000);
		
		// Should show feedback content
		await expect(page.locator('h1, h2').filter({ hasText: 'Feedback' })).toBeVisible();
		
		// Test mobile layout
		await page.setViewportSize({ width: 375, height: 667 });
		await page.waitForTimeout(1000);
		
		// Should still show content
		await expect(page.locator('h1, h2').filter({ hasText: 'Feedback' })).toBeVisible();
	});

	test('should handle feedback search functionality', async ({ page }) => {
		// Wait for feedback data to load
		await page.waitForTimeout(2000);
		
		// Look for search input
		const searchInput = page.locator('input[placeholder*="search"], input[type="search"]');
		
		if (await searchInput.isVisible()) {
			// Try searching
			await searchInput.fill('test');
			
			// Look for search button or press Enter
			const searchButton = page.locator('button').filter({ hasText: 'Search' });
			if (await searchButton.isVisible()) {
				await searchButton.click();
			} else {
				await page.keyboard.press('Enter');
			}
			
			// Wait for search results
			await page.waitForTimeout(1000);
			
			// Should still show feedback page
			await expect(page.locator('h1, h2').filter({ hasText: 'Feedback' })).toBeVisible();
		}
	});
});
