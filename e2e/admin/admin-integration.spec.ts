import { test, expect } from '@playwright/test';

test.describe('Admin Integration Tests', () => {
	test.beforeEach(async ({ page }) => {
		// Login as admin before each test
		await page.goto('/admin/login');
		await page.fill('input[id="username"]', 'admin');
		await page.fill('input[id="password"]', 'admin123');
		await page.click('button[type="submit"]');
		await page.waitForURL('/admin');
	});

	test('should complete full admin workflow', async ({ page }) => {
		// 1. Start at dashboard
		await expect(page.locator('h1')).toContainText('Admin Dashboard');
		
		// 2. Navigate to user management
		await page.click('text=Users');
		await page.waitForURL('/admin/users');
		await expect(page.locator('h1')).toContainText('User Management');
		
		// 3. Search for a user
		await page.fill('input[placeholder="Search users..."]', 'admin');
		await page.click('button').filter({ hasText: 'Search' });
		await page.waitForTimeout(1000);
		
		// 4. Navigate to collections
		await page.click('text=Collections');
		await page.waitForURL('/admin/collections');
		await expect(page.locator('h1')).toContainText('Collection Management');
		
		// 5. Navigate to system monitoring
		await page.click('text=System');
		await page.waitForURL('/admin/system');
		await expect(page.locator('h1')).toContainText('System Monitoring');
		
		// 6. Clear cache
		const clearCacheButton = page.locator('button').filter({ hasText: 'Clear Cache' });
		await clearCacheButton.click();
		await page.waitForTimeout(2000);
		
		// 7. Navigate to audit logs
		await page.click('text=Audit Logs');
		await page.waitForURL('/admin/audit');
		await expect(page.locator('h1')).toContainText('Audit Logs');
		
		// 8. Filter audit logs
		await page.fill('input[placeholder*="action"]', 'admin_login');
		await page.click('button').filter({ hasText: 'Filter' });
		await page.waitForTimeout(1000);
		
		// 9. Navigate to feedback
		await page.click('text=Feedback');
		await page.waitForURL('/admin/feedback');
		
		// 10. Return to dashboard
		await page.click('text=Dashboard');
		await page.waitForURL('/admin');
		await expect(page.locator('h1')).toContainText('Admin Dashboard');
	});

	test('should maintain session across navigation', async ({ page }) => {
		// Navigate through all admin pages
		const pages = [
			{ link: 'Users', url: '/admin/users', title: 'User Management' },
			{ link: 'Collections', url: '/admin/collections', title: 'Collection Management' },
			{ link: 'System', url: '/admin/system', title: 'System Monitoring' },
			{ link: 'Audit Logs', url: '/admin/audit', title: 'Audit Logs' },
			{ link: 'Feedback', url: '/admin/feedback', title: 'Feedback' },
		];
		
		for (const adminPage of pages) {
			await page.click(`text=${adminPage.link}`);
			await page.waitForURL(adminPage.url);
			
			// Should not redirect to login (session maintained)
			await expect(page).toHaveURL(adminPage.url);
			
			// Should show admin content, not login page
			await expect(page.locator('text=Admin Panel')).toBeVisible();
		}
	});

	test('should handle concurrent admin operations', async ({ page }) => {
		// Navigate to system monitoring
		await page.click('text=System');
		await page.waitForURL('/admin/system');
		
		// Perform multiple operations
		const refreshButton = page.locator('button').filter({ hasText: 'Refresh' });
		const clearCacheButton = page.locator('button').filter({ hasText: 'Clear Cache' });
		
		// Click both buttons in quick succession
		await Promise.all([
			refreshButton.click(),
			clearCacheButton.click(),
		]);
		
		// Wait for operations to complete
		await page.waitForTimeout(3000);
		
		// Page should still be functional
		await expect(page.locator('h1')).toContainText('System Monitoring');
		await expect(page.locator('text=System Health')).toBeVisible();
	});

	test('should handle admin logout and re-login', async ({ page }) => {
		// Verify we're logged in
		await expect(page.locator('text=Admin Panel')).toBeVisible();
		
		// Logout
		const logoutButton = page.locator('button').filter({ hasText: 'Logout' });
		await logoutButton.click();
		
		// Should redirect to login
		await page.waitForURL('/admin/login');
		await expect(page.locator('h1')).toContainText('Admin Login');
		
		// Try to access protected route
		await page.goto('/admin/users');
		
		// Should redirect back to login
		await page.waitForURL('/admin/login');
		
		// Re-login
		await page.fill('input[id="username"]', 'admin');
		await page.fill('input[id="password"]', 'admin123');
		await page.click('button[type="submit"]');
		
		// Should be able to access admin dashboard
		await page.waitForURL('/admin');
		await expect(page.locator('h1')).toContainText('Admin Dashboard');
	});

	test('should handle browser refresh on admin pages', async ({ page }) => {
		// Navigate to user management
		await page.click('text=Users');
		await page.waitForURL('/admin/users');
		
		// Refresh the page
		await page.reload();
		
		// Should still be on the same page (session maintained)
		await expect(page).toHaveURL('/admin/users');
		await expect(page.locator('h1')).toContainText('User Management');
		await expect(page.locator('text=Admin Panel')).toBeVisible();
	});

	test('should handle admin operations with error recovery', async ({ page }) => {
		// Navigate to user management
		await page.click('text=Users');
		await page.waitForURL('/admin/users');
		
		// Try to search for non-existent user
		await page.fill('input[placeholder="Search users..."]', 'nonexistentuser12345');
		await page.click('button').filter({ hasText: 'Search' });
		await page.waitForTimeout(1000);
		
		// Clear search and try again
		await page.fill('input[placeholder="Search users..."]', '');
		await page.click('button').filter({ hasText: 'Search' });
		await page.waitForTimeout(1000);
		
		// Should still be functional
		await expect(page.locator('h1')).toContainText('User Management');
		await expect(page.locator('table')).toBeVisible();
	});

	test('should maintain admin privileges throughout session', async ({ page }) => {
		// Test access to all admin sections
		const adminSections = [
			'/admin',
			'/admin/users',
			'/admin/collections',
			'/admin/system',
			'/admin/audit',
			'/admin/feedback',
		];
		
		for (const section of adminSections) {
			await page.goto(section);
			
			// Should not redirect to login
			await expect(page).toHaveURL(section);
			
			// Should show admin interface
			await expect(page.locator('text=Admin Panel')).toBeVisible();
			
			// Should not show access denied
			await expect(page.locator('text=Access denied')).not.toBeVisible();
			await expect(page.locator('text=Forbidden')).not.toBeVisible();
		}
	});

	test('should handle mobile responsive admin interface', async ({ page }) => {
		// Set mobile viewport
		await page.setViewportSize({ width: 375, height: 667 });
		
		// Navigate through admin sections on mobile
		const sections = ['Users', 'Collections', 'System', 'Feedback'];
		
		for (const section of sections) {
			// Open mobile menu if needed
			const mobileMenuButton = page.locator('button').filter({ has: page.locator('svg') }).first();
			if (await mobileMenuButton.isVisible()) {
				await mobileMenuButton.click();
			}
			
			// Click section
			await page.click(`text=${section}`);
			await page.waitForTimeout(1000);
			
			// Should show content
			await expect(page.locator('h1')).toBeVisible();
			
			// Close mobile menu if it's open
			const closeButton = page.locator('button').filter({ has: page.locator('svg') }).last();
			if (await closeButton.isVisible()) {
				await closeButton.click();
			}
		}
	});

	test('should handle admin data consistency', async ({ page }) => {
		// Get initial user count from dashboard
		await expect(page.locator('text=Total Users')).toBeVisible();
		const dashboardUserCount = await page.locator('text=Total Users').locator('..').locator('[class*="text-2xl"]').textContent();
		
		// Navigate to user management
		await page.click('text=Users');
		await page.waitForURL('/admin/users');
		
		// Check user count in user management
		await page.waitForSelector('table');
		const userTableExists = await page.locator('table').isVisible();
		
		if (userTableExists) {
			// Data should be consistent between dashboard and user management
			const userRows = page.locator('tbody tr');
			const userRowCount = await userRows.count();
			
			// Should have users (at least the admin user)
			expect(userRowCount).toBeGreaterThan(0);
		}
		
		// Navigate back to dashboard
		await page.click('text=Dashboard');
		await page.waitForURL('/admin');
		
		// User count should still be the same
		await expect(page.locator('text=Total Users')).toBeVisible();
	});

	test('should handle admin error states gracefully', async ({ page }) => {
		// Navigate to system monitoring
		await page.click('text=System');
		await page.waitForURL('/admin/system');
		
		// Wait for page to load
		await page.waitForTimeout(3000);
		
		// Check if error state is handled gracefully
		const errorMessage = page.locator('text=Failed to load');
		const tryAgainButton = page.locator('button').filter({ hasText: 'Try Again' });
		
		if (await errorMessage.isVisible()) {
			// Should show try again button
			await expect(tryAgainButton).toBeVisible();
			
			// Click try again
			await tryAgainButton.click();
			await page.waitForTimeout(2000);
			
			// Should eventually show content or maintain error state gracefully
			const hasContent = await page.locator('text=System Health').isVisible();
			const stillHasError = await errorMessage.isVisible();
			
			expect(hasContent || stillHasError).toBe(true);
		} else {
			// If no error, should show normal content
			await expect(page.locator('text=System Health')).toBeVisible();
		}
	});
});
