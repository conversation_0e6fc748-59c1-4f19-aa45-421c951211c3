import { chromium, FullConfig } from '@playwright/test';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

/**
 * Global setup for admin dashboard tests
 * 
 * This setup ensures:
 * 1. Database is running and migrated
 * 2. Admin accounts are seeded
 * 3. Sample data is available for testing
 * 4. Application is ready for testing
 */
async function globalSetup(config: FullConfig) {
	console.log('🚀 Starting admin dashboard test setup...');
	
	try {
		// 1. Ensure database containers are running
		console.log('📦 Starting database containers...');
		await execAsync('yarn dup');
		
		// Wait for database to be ready
		await new Promise(resolve => setTimeout(resolve, 5000));
		
		// 2. Run database migrations
		console.log('🗄️ Running database migrations...');
		await execAsync('yarn p:m');
		
		// 3. Seed admin accounts and sample data
		console.log('🌱 Seeding admin accounts and sample data...');
		await execAsync('yarn seed:admin:sample');
		
		// 4. Verify application is accessible
		console.log('🔍 Verifying application accessibility...');
		const browser = await chromium.launch();
		const page = await browser.newPage();
		
		try {
			// Check if the application is running
			await page.goto('http://localhost:3001/admin/login', { 
				waitUntil: 'networkidle',
				timeout: 30000 
			});
			
			// Verify login page loads
			await page.waitForSelector('h1', { timeout: 10000 });
			const title = await page.textContent('h1');
			
			if (!title?.includes('Admin Login')) {
				throw new Error('Admin login page not accessible');
			}
			
			console.log('✅ Application is accessible');
			
			// Test admin login to ensure credentials work
			console.log('🔐 Testing admin login...');
			await page.fill('input[id="username"]', 'admin');
			await page.fill('input[id="password"]', 'admin123');
			await page.click('button[type="submit"]');
			
			// Wait for redirect to admin dashboard
			await page.waitForURL('**/admin', { timeout: 10000 });
			
			// Verify dashboard loads
			await page.waitForSelector('h1', { timeout: 10000 });
			const dashboardTitle = await page.textContent('h1');
			
			if (!dashboardTitle?.includes('Admin Dashboard')) {
				throw new Error('Admin dashboard not accessible after login');
			}
			
			console.log('✅ Admin login successful');
			
		} catch (error) {
			console.error('❌ Application verification failed:', error);
			throw error;
		} finally {
			await browser.close();
		}
		
		// 5. Setup test data state
		console.log('📊 Setting up test data state...');
		
		// Create additional test data if needed
		// This could include creating test collections, feedback, etc.
		
		console.log('🎉 Admin dashboard test setup completed successfully!');
		console.log('');
		console.log('📋 Test Environment Ready:');
		console.log('  • Database: Running with migrations applied');
		console.log('  • Admin Accounts: admin/admin123, superadmin/superadmin123');
		console.log('  • Sample Data: Users, collections, feedback available');
		console.log('  • Application: Accessible at http://localhost:3001');
		console.log('');
		
	} catch (error) {
		console.error('❌ Admin dashboard test setup failed:', error);
		console.error('');
		console.error('🔧 Troubleshooting:');
		console.error('  1. Ensure Docker is running');
		console.error('  2. Check database connection');
		console.error('  3. Verify environment variables');
		console.error('  4. Run yarn dev manually to check for errors');
		console.error('');
		
		// Cleanup on failure
		try {
			console.log('🧹 Cleaning up after setup failure...');
			await execAsync('docker-compose down');
		} catch (cleanupError) {
			console.error('Failed to cleanup:', cleanupError);
		}
		
		throw error;
	}
}

export default globalSetup;
