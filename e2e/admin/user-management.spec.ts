import { test, expect } from '@playwright/test';

test.describe('User Management', () => {
	test.beforeEach(async ({ page }) => {
		// Login as admin and navigate to user management
		await page.goto('/admin/login');
		await page.fill('input[id="username"]', 'admin');
		await page.fill('input[id="password"]', 'admin123');
		await page.click('button[type="submit"]');
		await page.waitForURL('/admin');
		
		// Navigate to user management
		await page.click('text=Users');
		await page.waitForURL('/admin/users');
	});

	test('should display user management page correctly', async ({ page }) => {
		// Check page title and description
		await expect(page.locator('h1')).toContainText('User Management');
		await expect(page.locator('text=Manage user accounts and permissions')).toBeVisible();
		
		// Check refresh button
		await expect(page.locator('button').filter({ hasText: 'Refresh' })).toBeVisible();
		
		// Check search functionality
		await expect(page.locator('input[placeholder="Search users..."]')).toBeVisible();
		await expect(page.locator('button').filter({ hasText: 'Search' })).toBeVisible();
		
		// Check users table
		await expect(page.locator('text=Users (')).toBeVisible();
		await expect(page.locator('table')).toBeVisible();
	});

	test('should display users table with correct columns', async ({ page }) => {
		// Wait for table to load
		await page.waitForSelector('table');
		
		// Check table headers
		await expect(page.locator('th').filter({ hasText: 'User' })).toBeVisible();
		await expect(page.locator('th').filter({ hasText: 'Provider' })).toBeVisible();
		await expect(page.locator('th').filter({ hasText: 'Role' })).toBeVisible();
		await expect(page.locator('th').filter({ hasText: 'Status' })).toBeVisible();
		await expect(page.locator('th').filter({ hasText: 'Created' })).toBeVisible();
		await expect(page.locator('th').filter({ hasText: 'Actions' })).toBeVisible();
	});

	test('should display user data in table rows', async ({ page }) => {
		// Wait for table data to load
		await page.waitForSelector('tbody tr');
		
		// Check that at least one user row exists
		const userRows = page.locator('tbody tr');
		await expect(userRows.first()).toBeVisible();
		
		// Check for admin user (should exist from seeding)
		await expect(page.locator('text=admin')).toBeVisible();
		
		// Check for role badges
		await expect(page.locator('text=ADMIN').or(page.locator('text=USER'))).toBeVisible();
		
		// Check for status badges
		await expect(page.locator('text=Active').or(page.locator('text=Disabled'))).toBeVisible();
	});

	test('should search users successfully', async ({ page }) => {
		// Wait for initial load
		await page.waitForSelector('table');
		
		// Search for admin user
		await page.fill('input[placeholder="Search users..."]', 'admin');
		await page.click('button').filter({ hasText: 'Search' });
		
		// Wait for search results
		await page.waitForTimeout(1000);
		
		// Should show admin user in results
		await expect(page.locator('tbody').locator('text=admin')).toBeVisible();
	});

	test('should update user role successfully', async ({ page }) => {
		// Wait for table to load
		await page.waitForSelector('tbody tr');
		
		// Find a user row (not the current admin)
		const userRows = page.locator('tbody tr');
		const firstRow = userRows.first();
		
		// Find role select dropdown in the first row
		const roleSelect = firstRow.locator('select, [role="combobox"]').first();
		
		if (await roleSelect.isVisible()) {
			// Get current role
			const currentRole = await roleSelect.textContent();
			
			// Click to open dropdown
			await roleSelect.click();
			
			// Select different role
			if (currentRole?.includes('USER')) {
				await page.locator('text=Admin').click();
			} else {
				await page.locator('text=User').click();
			}
			
			// Wait for update to complete
			await page.waitForTimeout(1000);
			
			// Check for success message
			await expect(page.locator('text=User role updated').or(page.locator('text=updated'))).toBeVisible();
		}
	});

	test('should toggle user status successfully', async ({ page }) => {
		// Wait for table to load
		await page.waitForSelector('tbody tr');
		
		// Find a user row (preferably not the current admin)
		const userRows = page.locator('tbody tr');
		
		// Look for a non-admin user to avoid disabling current admin
		let targetRow = null;
		const rowCount = await userRows.count();
		
		for (let i = 0; i < rowCount; i++) {
			const row = userRows.nth(i);
			const username = await row.locator('td').first().textContent();
			
			// Skip the current admin user
			if (username && !username.includes('admin')) {
				targetRow = row;
				break;
			}
		}
		
		if (targetRow) {
			// Find the action button (enable/disable)
			const actionButton = targetRow.locator('button').last();
			await actionButton.click();
			
			// Wait for update
			await page.waitForTimeout(1000);
			
			// Check for success message
			await expect(page.locator('text=successfully').or(page.locator('text=updated'))).toBeVisible();
		}
	});

	test('should refresh user data', async ({ page }) => {
		// Wait for initial load
		await page.waitForSelector('table');
		
		// Click refresh button
		const refreshButton = page.locator('button').filter({ hasText: 'Refresh' });
		await refreshButton.click();
		
		// Wait for refresh to complete
		await page.waitForTimeout(1000);
		
		// Verify table is still visible
		await expect(page.locator('table')).toBeVisible();
		await expect(page.locator('tbody tr')).toBeVisible();
	});

	test('should handle pagination if available', async ({ page }) => {
		// Wait for table to load
		await page.waitForSelector('table');
		
		// Check if pagination exists
		const nextButton = page.locator('button').filter({ hasText: 'Next' });
		const prevButton = page.locator('button').filter({ hasText: 'Previous' });
		
		if (await nextButton.isVisible()) {
			// Test pagination
			await nextButton.click();
			await page.waitForTimeout(1000);
			
			// Should still show table
			await expect(page.locator('table')).toBeVisible();
			
			// Test previous button
			if (await prevButton.isVisible()) {
				await prevButton.click();
				await page.waitForTimeout(1000);
				await expect(page.locator('table')).toBeVisible();
			}
		}
	});

	test('should display user count information', async ({ page }) => {
		// Wait for table to load
		await page.waitForSelector('table');
		
		// Check for user count in header
		const userCountHeader = page.locator('text=Users (').first();
		await expect(userCountHeader).toBeVisible();
		
		// Check for pagination info if available
		const paginationInfo = page.locator('text=Showing').first();
		if (await paginationInfo.isVisible()) {
			await expect(paginationInfo).toContainText('of');
		}
	});

	test('should handle empty search results', async ({ page }) => {
		// Wait for initial load
		await page.waitForSelector('table');
		
		// Search for non-existent user
		await page.fill('input[placeholder="Search users..."]', 'nonexistentuser12345');
		await page.click('button').filter({ hasText: 'Search' });
		
		// Wait for search to complete
		await page.waitForTimeout(1000);
		
		// Should show empty state or no results
		const tableBody = page.locator('tbody');
		const rowCount = await tableBody.locator('tr').count();
		
		// Either no rows or empty state message
		if (rowCount === 0) {
			// No rows found - this is expected
			expect(rowCount).toBe(0);
		} else {
			// Check if there's an empty state message
			await expect(page.locator('text=No users found').or(page.locator('text=No results'))).toBeVisible();
		}
	});

	test('should display user icons correctly', async ({ page }) => {
		// Wait for table to load
		await page.waitForSelector('tbody tr');
		
		// Check for user icons (admin shield, regular user icon)
		const userRows = page.locator('tbody tr');
		const firstRow = userRows.first();
		
		// Should have either shield icon (admin) or user icon
		const hasIcon = await firstRow.locator('svg').first().isVisible();
		expect(hasIcon).toBe(true);
	});

	test('should handle loading states', async ({ page }) => {
		// Check that page loads without showing loading spinner indefinitely
		await page.waitForSelector('table', { timeout: 10000 });
		
		// Loading spinner should not be visible after data loads
		const loadingSpinner = page.locator('[class*="animate-spin"]');
		if (await loadingSpinner.isVisible()) {
			// Wait for loading to complete
			await loadingSpinner.waitFor({ state: 'hidden', timeout: 5000 });
		}
		
		// Table should be visible
		await expect(page.locator('table')).toBeVisible();
	});
});
