import { test, expect } from '@playwright/test';

test.describe('Audit Logs', () => {
	test.beforeEach(async ({ page }) => {
		// Login as admin and navigate to audit logs
		await page.goto('/admin/login');
		await page.fill('input[id="username"]', 'admin');
		await page.fill('input[id="password"]', 'admin123');
		await page.click('button[type="submit"]');
		await page.waitForURL('/admin');
		
		// Navigate to audit logs
		await page.click('text=Audit Logs');
		await page.waitForURL('/admin/audit');
	});

	test('should display audit logs page correctly', async ({ page }) => {
		// Check page title and description
		await expect(page.locator('h1')).toContainText('Audit Logs');
		await expect(page.locator('text=System activity and security audit trail')).toBeVisible();
		
		// Check refresh button
		await expect(page.locator('button').filter({ hasText: 'Refresh' })).toBeVisible();
	});

	test('should display filters section', async ({ page }) => {
		// Check filters section
		await expect(page.locator('text=Filters')).toBeVisible();
		
		// Check filter inputs
		await expect(page.locator('input[placeholder*="action"]')).toBeVisible();
		await expect(page.locator('input[placeholder*="user"]')).toBeVisible();
		
		// Check resource filter dropdown
		const resourceSelect = page.locator('select, [role="combobox"]').filter({ hasText: /resource|All resources/ });
		await expect(resourceSelect.first()).toBeVisible();
		
		// Check filter and clear buttons
		await expect(page.locator('button').filter({ hasText: 'Filter' })).toBeVisible();
		await expect(page.locator('button').filter({ hasText: 'Clear' })).toBeVisible();
	});

	test('should display audit events section', async ({ page }) => {
		// Wait for audit events to load
		await page.waitForTimeout(2000);
		
		// Check audit events section
		await expect(page.locator('text=Audit Events')).toBeVisible();
		
		// Either show events or empty state
		const hasEvents = await page.locator('[class*="border"]').filter({ has: page.locator('text=admin_login') }).isVisible();
		const hasEmptyState = await page.locator('text=No audit logs found').isVisible();
		
		expect(hasEvents || hasEmptyState).toBe(true);
	});

	test('should display audit event details', async ({ page }) => {
		// Wait for audit events to load
		await page.waitForTimeout(2000);
		
		// Look for audit event entries
		const eventEntries = page.locator('[class*="border"]').filter({ has: page.locator('[class*="badge"]') });
		const eventCount = await eventEntries.count();
		
		if (eventCount > 0) {
			const firstEvent = eventEntries.first();
			
			// Should have action badge
			await expect(firstEvent.locator('[class*="badge"]')).toBeVisible();
			
			// Should have resource badge
			await expect(firstEvent.locator('[class*="bg-"]').nth(1)).toBeVisible();
			
			// Should have timestamp
			await expect(firstEvent.locator('text=/\\d{1,2}\/\\d{1,2}\/\\d{4}|\\d{4}-\\d{2}-\\d{2}/')).toBeVisible();
			
			// Should have event details
			await expect(firstEvent.locator('text={')).toBeVisible(); // JSON details
		}
	});

	test('should filter audit logs by action', async ({ page }) => {
		// Wait for initial load
		await page.waitForTimeout(2000);
		
		// Fill action filter
		const actionInput = page.locator('input[placeholder*="action"]');
		await actionInput.fill('admin_login');
		
		// Click filter button
		await page.click('button').filter({ hasText: 'Filter' });
		
		// Wait for filter to apply
		await page.waitForTimeout(1000);
		
		// Should show filtered results or empty state
		const hasResults = await page.locator('text=admin_login').isVisible();
		const hasEmptyState = await page.locator('text=No audit logs found').isVisible();
		
		expect(hasResults || hasEmptyState).toBe(true);
	});

	test('should filter audit logs by resource', async ({ page }) => {
		// Wait for initial load
		await page.waitForTimeout(2000);
		
		// Open resource filter dropdown
		const resourceSelect = page.locator('select, [role="combobox"]').first();
		await resourceSelect.click();
		
		// Select a resource type
		const userOption = page.locator('text=User').or(page.locator('option[value="user"]'));
		if (await userOption.isVisible()) {
			await userOption.click();
			
			// Click filter button
			await page.click('button').filter({ hasText: 'Filter' });
			
			// Wait for filter to apply
			await page.waitForTimeout(1000);
			
			// Should show filtered results
			await expect(page.locator('text=Audit Events')).toBeVisible();
		}
	});

	test('should filter audit logs by user ID', async ({ page }) => {
		// Wait for initial load
		await page.waitForTimeout(2000);
		
		// Fill user ID filter
		const userIdInput = page.locator('input[placeholder*="user"]');
		await userIdInput.fill('test-user-id');
		
		// Click filter button
		await page.click('button').filter({ hasText: 'Filter' });
		
		// Wait for filter to apply
		await page.waitForTimeout(1000);
		
		// Should show filtered results or empty state
		const hasResults = await page.locator('text=test-user-id').isVisible();
		const hasEmptyState = await page.locator('text=No audit logs found').isVisible();
		
		expect(hasResults || hasEmptyState).toBe(true);
	});

	test('should clear filters', async ({ page }) => {
		// Wait for initial load
		await page.waitForTimeout(2000);
		
		// Fill some filters
		await page.fill('input[placeholder*="action"]', 'test_action');
		await page.fill('input[placeholder*="user"]', 'test_user');
		
		// Click clear button
		await page.click('button').filter({ hasText: 'Clear' });
		
		// Filters should be cleared
		await expect(page.locator('input[placeholder*="action"]')).toHaveValue('');
		await expect(page.locator('input[placeholder*="user"]')).toHaveValue('');
	});

	test('should display event badges with correct colors', async ({ page }) => {
		// Wait for audit events to load
		await page.waitForTimeout(2000);
		
		const eventEntries = page.locator('[class*="border"]').filter({ has: page.locator('[class*="badge"]') });
		const eventCount = await eventEntries.count();
		
		if (eventCount > 0) {
			const firstEvent = eventEntries.first();
			
			// Check for colored badges
			const actionBadge = firstEvent.locator('[class*="bg-green-100"], [class*="bg-red-100"], [class*="bg-blue-100"], [class*="bg-yellow-100"]').first();
			await expect(actionBadge).toBeVisible();
			
			// Check for resource badge
			const resourceBadge = firstEvent.locator('[class*="border"]').first();
			await expect(resourceBadge).toBeVisible();
		}
	});

	test('should display event icons correctly', async ({ page }) => {
		// Wait for audit events to load
		await page.waitForTimeout(2000);
		
		const eventEntries = page.locator('[class*="border"]').filter({ has: page.locator('svg') });
		const eventCount = await eventEntries.count();
		
		if (eventCount > 0) {
			const firstEvent = eventEntries.first();
			
			// Should have resource icon
			await expect(firstEvent.locator('svg').first()).toBeVisible();
		}
	});

	test('should display event metadata', async ({ page }) => {
		// Wait for audit events to load
		await page.waitForTimeout(2000);
		
		const eventEntries = page.locator('[class*="border"]').filter({ has: page.locator('[class*="badge"]') });
		const eventCount = await eventEntries.count();
		
		if (eventCount > 0) {
			const firstEvent = eventEntries.first();
			
			// Should have timestamp
			await expect(firstEvent.locator('svg').filter({ has: page.locator('path') })).toBeVisible();
			
			// Should have user or admin information
			const hasUserInfo = await firstEvent.locator('text=User:').or(firstEvent.locator('text=Admin:')).isVisible();
			if (hasUserInfo) {
				await expect(firstEvent.locator('text=User:').or(firstEvent.locator('text=Admin:'))).toBeVisible();
			}
		}
	});

	test('should handle pagination if available', async ({ page }) => {
		// Wait for audit events to load
		await page.waitForTimeout(2000);
		
		// Check if pagination exists
		const nextButton = page.locator('button').filter({ hasText: 'Next' });
		const prevButton = page.locator('button').filter({ hasText: 'Previous' });
		
		if (await nextButton.isVisible()) {
			// Test pagination
			await nextButton.click();
			await page.waitForTimeout(1000);
			
			// Should still show audit logs page
			await expect(page.locator('h1')).toContainText('Audit Logs');
			
			// Test previous button
			if (await prevButton.isVisible()) {
				await prevButton.click();
				await page.waitForTimeout(1000);
				await expect(page.locator('h1')).toContainText('Audit Logs');
			}
		}
	});

	test('should display pagination information', async ({ page }) => {
		// Wait for audit events to load
		await page.waitForTimeout(2000);
		
		// Check for pagination info
		const paginationInfo = page.locator('text=Showing').first();
		if (await paginationInfo.isVisible()) {
			await expect(paginationInfo).toContainText('of');
			await expect(paginationInfo).toContainText('logs');
		}
	});

	test('should refresh audit logs', async ({ page }) => {
		// Wait for initial load
		await page.waitForTimeout(2000);
		
		// Click refresh button
		const refreshButton = page.locator('button').filter({ hasText: 'Refresh' });
		await refreshButton.click();
		
		// Wait for refresh to complete
		await page.waitForTimeout(1000);
		
		// Verify page is still functional
		await expect(page.locator('h1')).toContainText('Audit Logs');
		await expect(page.locator('text=Filters')).toBeVisible();
	});

	test('should handle empty audit logs state', async ({ page }) => {
		// Wait for audit events to load
		await page.waitForTimeout(2000);
		
		// Check if empty state is shown
		const emptyState = page.locator('text=No audit logs found');
		const hasEvents = await page.locator('[class*="border"]').filter({ has: page.locator('[class*="badge"]') }).isVisible();
		
		if (!hasEvents) {
			await expect(emptyState).toBeVisible();
			
			// Should show file icon in empty state
			const fileIcon = page.locator('svg').filter({ has: page.locator('path') });
			await expect(fileIcon.first()).toBeVisible();
		}
	});

	test('should display JSON event details', async ({ page }) => {
		// Wait for audit events to load
		await page.waitForTimeout(2000);
		
		const eventEntries = page.locator('[class*="border"]').filter({ has: page.locator('[class*="badge"]') });
		const eventCount = await eventEntries.count();
		
		if (eventCount > 0) {
			const firstEvent = eventEntries.first();
			
			// Should have JSON details
			const jsonDetails = firstEvent.locator('text={').or(firstEvent.locator('pre'));
			if (await jsonDetails.isVisible()) {
				await expect(jsonDetails).toBeVisible();
			}
		}
	});

	test('should handle loading states', async ({ page }) => {
		// Check that page loads without showing loading spinner indefinitely
		await page.waitForTimeout(3000);
		
		// Loading spinner should not be visible after data loads
		const loadingSpinner = page.locator('[class*="animate-spin"]');
		if (await loadingSpinner.isVisible()) {
			// Wait for loading to complete
			await loadingSpinner.waitFor({ state: 'hidden', timeout: 5000 });
		}
		
		// Page content should be visible
		await expect(page.locator('h1')).toContainText('Audit Logs');
		await expect(page.locator('text=Filters')).toBeVisible();
	});

	test('should maintain responsive layout', async ({ page }) => {
		// Test desktop layout
		await page.setViewportSize({ width: 1200, height: 800 });
		await page.waitForTimeout(1000);
		
		// Should show filters in grid layout
		await expect(page.locator('text=Filters')).toBeVisible();
		
		// Test mobile layout
		await page.setViewportSize({ width: 375, height: 667 });
		await page.waitForTimeout(1000);
		
		// Should still show all content
		await expect(page.locator('h1')).toContainText('Audit Logs');
		await expect(page.locator('text=Filters')).toBeVisible();
	});
});
