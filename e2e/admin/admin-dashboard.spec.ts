import { test, expect } from '@playwright/test';

test.describe('Admin Dashboard', () => {
	test.beforeEach(async ({ page }) => {
		// Login as admin before each test
		await page.goto('/admin/login');
		await page.fill('input[id="username"]', 'admin');
		await page.fill('input[id="password"]', 'admin123');
		await page.click('button[type="submit"]');
		await page.waitForURL('/admin');
	});

	test('should display dashboard overview correctly', async ({ page }) => {
		// Check main dashboard elements
		await expect(page.locator('h1')).toContainText('Admin Dashboard');
		await expect(page.locator('text=System overview and key metrics')).toBeVisible();
		
		// Check refresh button
		await expect(page.locator('button').filter({ hasText: 'Refresh' })).toBeVisible();
		
		// Check system health section
		await expect(page.locator('text=System Health')).toBeVisible();
		
		// Check stats cards
		await expect(page.locator('text=Total Users')).toBeVisible();
		await expect(page.locator('text=Collections')).toBeVisible();
		await expect(page.locator('text=Feedback')).toBeVisible();
		await expect(page.locator('text=Performance')).toBeVisible();
	});

	test('should display system health status', async ({ page }) => {
		// Wait for system health section to load
		await page.waitForSelector('text=System Health');
		
		// Check health status badge
		const healthBadge = page.locator('[class*="bg-green-100"], [class*="bg-yellow-100"], [class*="bg-red-100"]').first();
		await expect(healthBadge).toBeVisible();
		
		// Check health details
		await expect(page.locator('text=Database')).toBeVisible();
		await expect(page.locator('text=Cache')).toBeVisible();
		await expect(page.locator('text=AI Services')).toBeVisible();
	});

	test('should display user statistics', async ({ page }) => {
		// Wait for stats to load
		await page.waitForSelector('text=Total Users');
		
		// Check user stats card
		const userStatsCard = page.locator('text=Total Users').locator('..');
		await expect(userStatsCard).toBeVisible();
		
		// Check for numeric values (should be numbers)
		const totalUsersValue = page.locator('text=Total Users').locator('..').locator('.text-2xl').first();
		await expect(totalUsersValue).toBeVisible();
		
		// Check additional user metrics
		await expect(page.locator('text=Active:')).toBeVisible();
		await expect(page.locator('text=Admins:')).toBeVisible();
	});

	test('should display collection statistics', async ({ page }) => {
		// Wait for collections stats
		await page.waitForSelector('text=Collections');
		
		const collectionsCard = page.locator('text=Collections').locator('..');
		await expect(collectionsCard).toBeVisible();
		
		// Check collection metrics
		await expect(page.locator('text=Public:')).toBeVisible();
		await expect(page.locator('text=Private:')).toBeVisible();
	});

	test('should display feedback statistics', async ({ page }) => {
		// Wait for feedback stats
		await page.waitForSelector('text=Feedback');
		
		const feedbackCard = page.locator('text=Feedback').locator('..');
		await expect(feedbackCard).toBeVisible();
		
		// Check feedback metrics
		await expect(page.locator('text=pending')).toBeVisible();
		await expect(page.locator('text=resolved')).toBeVisible();
	});

	test('should display performance metrics', async ({ page }) => {
		// Wait for performance stats
		await page.waitForSelector('text=Performance');
		
		const performanceCard = page.locator('text=Performance').locator('..');
		await expect(performanceCard).toBeVisible();
		
		// Check performance metrics
		await expect(page.locator('text=Cache Hit:')).toBeVisible();
		await expect(page.locator('text=Error Rate:')).toBeVisible();
	});

	test('should refresh dashboard data', async ({ page }) => {
		// Wait for initial load
		await page.waitForSelector('text=System Health');
		
		// Click refresh button
		const refreshButton = page.locator('button').filter({ hasText: 'Refresh' });
		await refreshButton.click();
		
		// Check for loading state (spinner might be brief)
		// Just verify the button is still there after refresh
		await expect(refreshButton).toBeVisible();
		
		// Verify data is still displayed
		await expect(page.locator('text=System Health')).toBeVisible();
	});

	test('should navigate to different admin sections', async ({ page }) => {
		// Test navigation to Users
		await page.click('text=Users');
		await page.waitForURL('/admin/users');
		await expect(page.locator('h1')).toContainText('User Management');
		
		// Navigate back to dashboard
		await page.click('text=Dashboard');
		await page.waitForURL('/admin');
		await expect(page.locator('h1')).toContainText('Admin Dashboard');
		
		// Test navigation to Collections
		await page.click('text=Collections');
		await page.waitForURL('/admin/collections');
		await expect(page.locator('h1')).toContainText('Collection Management');
		
		// Navigate back to dashboard
		await page.click('text=Dashboard');
		await page.waitForURL('/admin');
		
		// Test navigation to Feedback
		await page.click('text=Feedback');
		await page.waitForURL('/admin/feedback');
		
		// Navigate back to dashboard
		await page.click('text=Dashboard');
		await page.waitForURL('/admin');
		
		// Test navigation to System
		await page.click('text=System');
		await page.waitForURL('/admin/system');
		await expect(page.locator('h1')).toContainText('System Monitoring');
	});

	test('should display sidebar navigation correctly', async ({ page }) => {
		// Check sidebar elements
		await expect(page.locator('text=Admin Panel')).toBeVisible();
		
		// Check navigation items
		await expect(page.locator('nav').locator('text=Dashboard')).toBeVisible();
		await expect(page.locator('nav').locator('text=Users')).toBeVisible();
		await expect(page.locator('nav').locator('text=Collections')).toBeVisible();
		await expect(page.locator('nav').locator('text=Feedback')).toBeVisible();
		await expect(page.locator('nav').locator('text=System')).toBeVisible();
		await expect(page.locator('nav').locator('text=Audit Logs')).toBeVisible();
		
		// Check logout button
		await expect(page.locator('button').filter({ hasText: 'Logout' })).toBeVisible();
	});

	test('should handle mobile navigation', async ({ page }) => {
		// Set mobile viewport
		await page.setViewportSize({ width: 375, height: 667 });
		
		// Check if mobile menu button is visible
		const mobileMenuButton = page.locator('button').filter({ has: page.locator('svg') }).first();
		
		// On mobile, sidebar should be hidden initially
		// Click mobile menu button if it exists
		if (await mobileMenuButton.isVisible()) {
			await mobileMenuButton.click();
			
			// Check if navigation becomes visible
			await expect(page.locator('nav')).toBeVisible();
		}
	});

	test('should display error state gracefully', async ({ page }) => {
		// This test would require mocking API failures
		// For now, we'll just verify the dashboard loads without errors
		await expect(page.locator('h1')).toContainText('Admin Dashboard');
		
		// Check that no error messages are displayed on successful load
		await expect(page.locator('text=Failed to load')).not.toBeVisible();
		await expect(page.locator('text=Error')).not.toBeVisible();
	});

	test('should maintain active navigation state', async ({ page }) => {
		// Dashboard should be active initially
		const dashboardNav = page.locator('nav').locator('text=Dashboard').locator('..');
		await expect(dashboardNav).toHaveClass(/bg-blue-600|default/);
		
		// Navigate to Users
		await page.click('text=Users');
		await page.waitForURL('/admin/users');
		
		// Users nav should now be active
		const usersNav = page.locator('nav').locator('text=Users').locator('..');
		await expect(usersNav).toHaveClass(/bg-blue-600|default/);
	});
});
