import { test, expect } from '@playwright/test';

test.describe('Collection Management', () => {
	test.beforeEach(async ({ page }) => {
		// Login as admin and navigate to collection management
		await page.goto('/admin/login');
		await page.fill('input[id="username"]', 'admin');
		await page.fill('input[id="password"]', 'admin123');
		await page.click('button[type="submit"]');
		await page.waitForURL('/admin');
		
		// Navigate to collection management
		await page.click('text=Collections');
		await page.waitForURL('/admin/collections');
	});

	test('should display collection management page correctly', async ({ page }) => {
		// Check page title and description
		await expect(page.locator('h1')).toContainText('Collection Management');
		await expect(page.locator('text=Manage user collections and content')).toBeVisible();
		
		// Check refresh button
		await expect(page.locator('button').filter({ hasText: 'Refresh' })).toBeVisible();
	});

	test('should display collections in grid layout', async ({ page }) => {
		// Wait for collections to load
		await page.waitForTimeout(2000);
		
		// Check if collections are displayed
		const collectionsGrid = page.locator('[class*="grid"]').filter({ has: page.locator('[class*="card"]') });
		
		// Either collections exist or empty state is shown
		const hasCollections = await page.locator('text=Words').first().isVisible();
		const hasEmptyState = await page.locator('text=No collections found').isVisible();
		
		expect(hasCollections || hasEmptyState).toBe(true);
	});

	test('should display collection cards with correct information', async ({ page }) => {
		// Wait for collections to load
		await page.waitForTimeout(2000);
		
		// Check if any collections exist
		const collectionCards = page.locator('[class*="card"]').filter({ has: page.locator('text=Words') });
		const cardCount = await collectionCards.count();
		
		if (cardCount > 0) {
			const firstCard = collectionCards.first();
			
			// Check for collection name
			await expect(firstCard.locator('h3, [class*="font-bold"]')).toBeVisible();
			
			// Check for language badges
			await expect(firstCard.locator('text=EN').or(firstCard.locator('text=VI'))).toBeVisible();
			
			// Check for content statistics
			await expect(firstCard.locator('text=Words')).toBeVisible();
			await expect(firstCard.locator('text=Paragraphs')).toBeVisible();
			await expect(firstCard.locator('text=Keywords')).toBeVisible();
			
			// Check for action buttons
			await expect(firstCard.locator('button')).toBeVisible();
		}
	});

	test('should display language badges correctly', async ({ page }) => {
		// Wait for collections to load
		await page.waitForTimeout(2000);
		
		const collectionCards = page.locator('[class*="card"]').filter({ has: page.locator('text=Words') });
		const cardCount = await collectionCards.count();
		
		if (cardCount > 0) {
			const firstCard = collectionCards.first();
			
			// Should have source and target language badges
			const languageBadges = firstCard.locator('[class*="bg-blue-100"], [class*="bg-green-100"]');
			const badgeCount = await languageBadges.count();
			
			// Should have at least 2 language badges (source and target)
			expect(badgeCount).toBeGreaterThanOrEqual(2);
			
			// Check for arrow between languages
			await expect(firstCard.locator('text=→')).toBeVisible();
		}
	});

	test('should display content statistics', async ({ page }) => {
		// Wait for collections to load
		await page.waitForTimeout(2000);
		
		const collectionCards = page.locator('[class*="card"]').filter({ has: page.locator('text=Words') });
		const cardCount = await collectionCards.count();
		
		if (cardCount > 0) {
			const firstCard = collectionCards.first();
			
			// Check for numeric statistics
			const wordsCount = firstCard.locator('text=Words').locator('..').locator('[class*="font-semibold"]');
			const paragraphsCount = firstCard.locator('text=Paragraphs').locator('..').locator('[class*="font-semibold"]');
			const keywordsCount = firstCard.locator('text=Keywords').locator('..').locator('[class*="font-semibold"]');
			
			await expect(wordsCount).toBeVisible();
			await expect(paragraphsCount).toBeVisible();
			await expect(keywordsCount).toBeVisible();
		}
	});

	test('should display collection metadata', async ({ page }) => {
		// Wait for collections to load
		await page.waitForTimeout(2000);
		
		const collectionCards = page.locator('[class*="card"]').filter({ has: page.locator('text=Words') });
		const cardCount = await collectionCards.count();
		
		if (cardCount > 0) {
			const firstCard = collectionCards.first();
			
			// Check for user ID
			await expect(firstCard.locator('text=User ID:')).toBeVisible();
			
			// Check for creation date
			await expect(firstCard.locator('text=Created:')).toBeVisible();
		}
	});

	test('should handle collection deletion with confirmation', async ({ page }) => {
		// Wait for collections to load
		await page.waitForTimeout(2000);
		
		const collectionCards = page.locator('[class*="card"]').filter({ has: page.locator('text=Words') });
		const cardCount = await collectionCards.count();
		
		if (cardCount > 0) {
			const firstCard = collectionCards.first();
			
			// Find delete button (trash icon)
			const deleteButton = firstCard.locator('button').filter({ has: page.locator('svg') }).last();
			
			// Set up dialog handler for confirmation
			page.on('dialog', async dialog => {
				expect(dialog.type()).toBe('confirm');
				expect(dialog.message()).toContain('Are you sure');
				await dialog.dismiss(); // Cancel deletion for test
			});
			
			await deleteButton.click();
			
			// Collection should still be there since we cancelled
			await expect(firstCard).toBeVisible();
		}
	});

	test('should handle view collection action', async ({ page }) => {
		// Wait for collections to load
		await page.waitForTimeout(2000);
		
		const collectionCards = page.locator('[class*="card"]').filter({ has: page.locator('text=Words') });
		const cardCount = await collectionCards.count();
		
		if (cardCount > 0) {
			const firstCard = collectionCards.first();
			
			// Find view button (eye icon)
			const viewButton = firstCard.locator('button').filter({ has: page.locator('svg') }).first();
			
			await viewButton.click();
			
			// Should show "coming soon" message or navigate to details
			await expect(page.locator('text=Coming soon').or(page.locator('text=View collection'))).toBeVisible();
		}
	});

	test('should refresh collections data', async ({ page }) => {
		// Wait for initial load
		await page.waitForTimeout(2000);
		
		// Click refresh button
		const refreshButton = page.locator('button').filter({ hasText: 'Refresh' });
		await refreshButton.click();
		
		// Wait for refresh to complete
		await page.waitForTimeout(1000);
		
		// Page should still be functional
		await expect(page.locator('h1')).toContainText('Collection Management');
	});

	test('should handle pagination if available', async ({ page }) => {
		// Wait for collections to load
		await page.waitForTimeout(2000);
		
		// Check if pagination exists
		const nextButton = page.locator('button').filter({ hasText: 'Next' });
		const prevButton = page.locator('button').filter({ hasText: 'Previous' });
		
		if (await nextButton.isVisible()) {
			// Test pagination
			await nextButton.click();
			await page.waitForTimeout(1000);
			
			// Should still show collections page
			await expect(page.locator('h1')).toContainText('Collection Management');
			
			// Test previous button
			if (await prevButton.isVisible()) {
				await prevButton.click();
				await page.waitForTimeout(1000);
				await expect(page.locator('h1')).toContainText('Collection Management');
			}
		}
	});

	test('should display pagination information', async ({ page }) => {
		// Wait for collections to load
		await page.waitForTimeout(2000);
		
		// Check for pagination info
		const paginationInfo = page.locator('text=Showing').first();
		if (await paginationInfo.isVisible()) {
			await expect(paginationInfo).toContainText('of');
			await expect(paginationInfo).toContainText('collections');
		}
	});

	test('should handle empty state correctly', async ({ page }) => {
		// Wait for collections to load
		await page.waitForTimeout(2000);
		
		// Check if empty state is shown
		const emptyState = page.locator('text=No collections found');
		const hasCollections = await page.locator('text=Words').first().isVisible();
		
		if (!hasCollections) {
			await expect(emptyState).toBeVisible();
			
			// Should show folder icon in empty state
			const folderIcon = page.locator('svg').filter({ has: page.locator('path') });
			await expect(folderIcon.first()).toBeVisible();
		}
	});

	test('should display collection icons correctly', async ({ page }) => {
		// Wait for collections to load
		await page.waitForTimeout(2000);
		
		const collectionCards = page.locator('[class*="card"]').filter({ has: page.locator('text=Words') });
		const cardCount = await collectionCards.count();
		
		if (cardCount > 0) {
			const firstCard = collectionCards.first();
			
			// Should have folder icon in header
			const folderIcon = firstCard.locator('svg').first();
			await expect(folderIcon).toBeVisible();
			
			// Should have action buttons with icons
			const actionButtons = firstCard.locator('button').filter({ has: page.locator('svg') });
			const buttonCount = await actionButtons.count();
			expect(buttonCount).toBeGreaterThanOrEqual(2); // View and delete buttons
		}
	});

	test('should handle loading states', async ({ page }) => {
		// Check that page loads without showing loading spinner indefinitely
		await page.waitForTimeout(3000);
		
		// Loading spinner should not be visible after data loads
		const loadingSpinner = page.locator('[class*="animate-spin"]');
		if (await loadingSpinner.isVisible()) {
			// Wait for loading to complete
			await loadingSpinner.waitFor({ state: 'hidden', timeout: 5000 });
		}
		
		// Page content should be visible
		await expect(page.locator('h1')).toContainText('Collection Management');
	});

	test('should maintain responsive layout', async ({ page }) => {
		// Test desktop layout
		await page.setViewportSize({ width: 1200, height: 800 });
		await page.waitForTimeout(1000);
		
		// Should show grid layout
		const grid = page.locator('[class*="grid"]');
		await expect(grid).toBeVisible();
		
		// Test mobile layout
		await page.setViewportSize({ width: 375, height: 667 });
		await page.waitForTimeout(1000);
		
		// Should still show content
		await expect(page.locator('h1')).toContainText('Collection Management');
	});
});
