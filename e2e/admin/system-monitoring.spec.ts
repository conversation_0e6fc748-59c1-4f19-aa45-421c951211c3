import { test, expect } from '@playwright/test';

test.describe('System Monitoring', () => {
	test.beforeEach(async ({ page }) => {
		// Login as admin and navigate to system monitoring
		await page.goto('/admin/login');
		await page.fill('input[id="username"]', 'admin');
		await page.fill('input[id="password"]', 'admin123');
		await page.click('button[type="submit"]');
		await page.waitForURL('/admin');
		
		// Navigate to system monitoring
		await page.click('text=System');
		await page.waitForURL('/admin/system');
	});

	test('should display system monitoring page correctly', async ({ page }) => {
		// Check page title and description
		await expect(page.locator('h1')).toContainText('System Monitoring');
		await expect(page.locator('text=Monitor system health and performance')).toBeVisible();
		
		// Check action buttons
		await expect(page.locator('button').filter({ hasText: 'Clear Cache' })).toBeVisible();
		await expect(page.locator('button').filter({ hasText: 'Refresh' })).toBeVisible();
	});

	test('should display system health overview', async ({ page }) => {
		// Wait for system health section to load
		await page.waitForSelector('text=System Health');
		
		// Check system health section
		await expect(page.locator('text=System Health')).toBeVisible();
		
		// Check health status badge
		const healthBadge = page.locator('[class*="bg-green-100"], [class*="bg-yellow-100"], [class*="bg-red-100"]').first();
		await expect(healthBadge).toBeVisible();
		
		// Check health status text
		await expect(page.locator('text=HEALTHY').or(page.locator('text=WARNING')).or(page.locator('text=ERROR'))).toBeVisible();
	});

	test('should display system components status', async ({ page }) => {
		// Wait for system health section
		await page.waitForSelector('text=System Health');
		
		// Check database status
		await expect(page.locator('text=Database')).toBeVisible();
		
		// Check cache status
		await expect(page.locator('text=Cache')).toBeVisible();
		
		// Check AI services status
		await expect(page.locator('text=AI Services')).toBeVisible();
		
		// Each component should have a status
		const databaseStatus = page.locator('text=Database').locator('..').locator('div').last();
		await expect(databaseStatus).toBeVisible();
	});

	test('should display AI token usage statistics', async ({ page }) => {
		// Wait for token usage section
		await page.waitForSelector('text=AI Token Usage');
		
		// Check token usage section
		await expect(page.locator('text=AI Token Usage')).toBeVisible();
		
		// Check token statistics cards
		await expect(page.locator('text=Total Tokens')).toBeVisible();
		await expect(page.locator('text=Total Cost')).toBeVisible();
		await expect(page.locator('text=API Requests')).toBeVisible();
		await expect(page.locator('text=Avg Tokens/Request')).toBeVisible();
		
		// Check for numeric values
		const totalTokensValue = page.locator('text=Total Tokens').locator('..').locator('[class*="text-2xl"]');
		await expect(totalTokensValue).toBeVisible();
		
		const totalCostValue = page.locator('text=Total Cost').locator('..').locator('[class*="text-2xl"]');
		await expect(totalCostValue).toBeVisible();
	});

	test('should display recent errors section', async ({ page }) => {
		// Wait for recent errors section
		await page.waitForSelector('text=Recent Errors');
		
		// Check recent errors section
		await expect(page.locator('text=Recent Errors')).toBeVisible();
		
		// Either show errors or "no recent errors" message
		const hasErrors = await page.locator('[class*="bg-red-50"]').isVisible();
		const noErrorsMessage = await page.locator('text=No recent errors found').isVisible();
		
		expect(hasErrors || noErrorsMessage).toBe(true);
		
		if (noErrorsMessage) {
			// Check for success icon
			await expect(page.locator('svg').filter({ has: page.locator('circle') })).toBeVisible();
		}
	});

	test('should handle cache clearing functionality', async ({ page }) => {
		// Wait for page to load
		await page.waitForSelector('text=System Health');
		
		// Click clear cache button
		const clearCacheButton = page.locator('button').filter({ hasText: 'Clear Cache' });
		await clearCacheButton.click();
		
		// Wait for operation to complete
		await page.waitForTimeout(2000);
		
		// Should show success message or update
		await expect(page.locator('text=Cache cleared').or(page.locator('text=successfully'))).toBeVisible();
	});

	test('should refresh system data', async ({ page }) => {
		// Wait for initial load
		await page.waitForSelector('text=System Health');
		
		// Click refresh button
		const refreshButton = page.locator('button').filter({ hasText: 'Refresh' });
		await refreshButton.click();
		
		// Wait for refresh to complete
		await page.waitForTimeout(1000);
		
		// Verify data is still displayed
		await expect(page.locator('text=System Health')).toBeVisible();
		await expect(page.locator('text=AI Token Usage')).toBeVisible();
	});

	test('should display health status icons correctly', async ({ page }) => {
		// Wait for system health section
		await page.waitForSelector('text=System Health');
		
		// Check for health status icon
		const healthIcon = page.locator('svg').filter({ has: page.locator('circle, path') }).first();
		await expect(healthIcon).toBeVisible();
		
		// Check component icons
		await expect(page.locator('text=Database').locator('..').locator('svg')).toBeVisible();
		await expect(page.locator('text=Cache').locator('..').locator('svg')).toBeVisible();
		await expect(page.locator('text=AI Services').locator('..').locator('svg')).toBeVisible();
	});

	test('should display token usage with proper formatting', async ({ page }) => {
		// Wait for token usage section
		await page.waitForSelector('text=AI Token Usage');
		
		// Check that cost is formatted with dollar sign
		const costElement = page.locator('text=Total Cost').locator('..').locator('[class*="text-2xl"]');
		const costText = await costElement.textContent();
		
		if (costText) {
			expect(costText).toMatch(/\$\d+\.\d{2}/); // Should be formatted as $X.XX
		}
		
		// Check that numbers are properly formatted with commas
		const tokensElement = page.locator('text=Total Tokens').locator('..').locator('[class*="text-2xl"]');
		await expect(tokensElement).toBeVisible();
	});

	test('should handle loading states properly', async ({ page }) => {
		// Check that page loads without showing loading spinner indefinitely
		await page.waitForSelector('text=System Health', { timeout: 10000 });
		
		// Loading spinner should not be visible after data loads
		const loadingSpinner = page.locator('[class*="animate-spin"]');
		if (await loadingSpinner.isVisible()) {
			// Wait for loading to complete
			await loadingSpinner.waitFor({ state: 'hidden', timeout: 5000 });
		}
		
		// All sections should be visible
		await expect(page.locator('text=System Health')).toBeVisible();
		await expect(page.locator('text=AI Token Usage')).toBeVisible();
		await expect(page.locator('text=Recent Errors')).toBeVisible();
	});

	test('should display error details when errors exist', async ({ page }) => {
		// Wait for recent errors section
		await page.waitForSelector('text=Recent Errors');
		
		// Check if there are any error entries
		const errorEntries = page.locator('[class*="bg-red-50"]');
		const errorCount = await errorEntries.count();
		
		if (errorCount > 0) {
			const firstError = errorEntries.first();
			
			// Should have error icon
			await expect(firstError.locator('svg')).toBeVisible();
			
			// Should have error message
			await expect(firstError.locator('[class*="font-medium"]')).toBeVisible();
			
			// Should have timestamp
			await expect(firstError.locator('text=:')).toBeVisible(); // Time format indicator
		}
	});

	test('should handle cache clearing loading state', async ({ page }) => {
		// Wait for page to load
		await page.waitForSelector('text=System Health');
		
		// Click clear cache button
		const clearCacheButton = page.locator('button').filter({ hasText: 'Clear Cache' });
		await clearCacheButton.click();
		
		// Check for loading state (might be brief)
		const buttonText = await clearCacheButton.textContent();
		
		// Button should either show loading or complete quickly
		expect(buttonText).toBeTruthy();
		
		// Wait for operation to complete
		await page.waitForTimeout(2000);
		
		// Button should be clickable again
		await expect(clearCacheButton).toBeEnabled();
	});

	test('should maintain responsive layout', async ({ page }) => {
		// Test desktop layout
		await page.setViewportSize({ width: 1200, height: 800 });
		await page.waitForTimeout(1000);
		
		// Should show grid layout for token usage
		await expect(page.locator('text=AI Token Usage')).toBeVisible();
		
		// Test mobile layout
		await page.setViewportSize({ width: 375, height: 667 });
		await page.waitForTimeout(1000);
		
		// Should still show all sections
		await expect(page.locator('text=System Health')).toBeVisible();
		await expect(page.locator('text=AI Token Usage')).toBeVisible();
	});

	test('should handle error state gracefully', async ({ page }) => {
		// Wait for page to load
		await page.waitForTimeout(3000);
		
		// Check that page loads successfully
		await expect(page.locator('h1')).toContainText('System Monitoring');
		
		// Should not show error messages on successful load
		await expect(page.locator('text=Failed to load')).not.toBeVisible();
		
		// If there's a "Try Again" button, it means there was an error
		const tryAgainButton = page.locator('button').filter({ hasText: 'Try Again' });
		if (await tryAgainButton.isVisible()) {
			// Click to retry
			await tryAgainButton.click();
			await page.waitForTimeout(2000);
			
			// Should eventually show content
			await expect(page.locator('text=System Health')).toBeVisible();
		}
	});
});
