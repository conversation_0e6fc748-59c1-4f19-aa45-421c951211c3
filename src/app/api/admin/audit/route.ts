import { NextRequest, NextResponse } from 'next/server';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { withAdminAuth } from '@/backend/middleware/admin.middleware';
import { getAuditService } from '@/backend/wire';
import { z } from 'zod';

// Validation schemas
const getAuditLogsQuerySchema = z.object({
	action: z.string().optional(),
	resource: z.string().optional(),
	user_id: z.string().optional(),
	admin_id: z.string().optional(),
	start_date: z
		.string()
		.optional()
		.transform((val) => (val ? new Date(val) : undefined)),
	end_date: z
		.string()
		.optional()
		.transform((val) => (val ? new Date(val) : undefined)),
	page: z
		.string()
		.optional()
		.transform((val) => (val ? parseInt(val, 10) : 1)),
	limit: z
		.string()
		.optional()
		.transform((val) => (val ? parseInt(val, 10) : 50)),
});

/**
 * GET /api/admin/audit
 * Get audit logs with filtering and pagination
 */
async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const query = getAuditLogsQuerySchema.parse({
			action: searchParams.get('action'),
			resource: searchParams.get('resource'),
			user_id: searchParams.get('user_id'),
			admin_id: searchParams.get('admin_id'),
			start_date: searchParams.get('start_date'),
			end_date: searchParams.get('end_date'),
			page: searchParams.get('page'),
			limit: searchParams.get('limit'),
		});

		const auditService = getAuditService();
		const offset = (query.page - 1) * query.limit;

		const [logs, total] = await Promise.all([
			auditService.getAuditLogs({
				action: query.action,
				resource: query.resource,
				user_id: query.user_id,
				admin_id: query.admin_id,
				start_date: query.start_date,
				end_date: query.end_date,
				limit: query.limit,
				offset,
			}),
			auditService.getAuditLogsCount({
				action: query.action,
				resource: query.resource,
				user_id: query.user_id,
				admin_id: query.admin_id,
				start_date: query.start_date,
				end_date: query.end_date,
			}),
		]);

		return NextResponse.json({
			success: true,
			data: {
				logs,
				total,
				page: query.page,
				limit: query.limit,
				totalPages: Math.ceil(total / query.limit),
			},
		});
	} catch (error) {
		console.error('Error fetching audit logs:', error);

		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{
					success: false,
					error: 'Validation error',
					details: error.errors,
				},
				{ status: 400 }
			);
		}

		return NextResponse.json(
			{
				success: false,
				error: 'Failed to fetch audit logs',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

// Apply admin authentication middleware
const wrappedGET = withAdminAuth(withErrorHandling(GET));
export { wrappedGET as GET };
