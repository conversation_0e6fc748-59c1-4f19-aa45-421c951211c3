import { NextRequest, NextResponse } from 'next/server';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { getAuthService } from '@/backend/wire';
import { generateToken } from '@/backend/utils/token.util';
import { getAuthConfig, getServerConfig } from '@/config';
import { cookies } from 'next/headers';
import { Role } from '@prisma/client';
import { z } from 'zod';

// Validation schema
const adminLoginSchema = z.object({
	username: z.string().min(1, 'Username is required'),
	password: z.string().min(1, 'Password is required'),
});

/**
 * POST /api/admin/auth
 * Admin login endpoint with role verification
 */
async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const { username, password } = adminLoginSchema.parse(body);

		const authService = getAuthService();
		const user = await authService.usernamePasswordLogin(username.trim(), password);

		// Verify admin role
		if (user.role !== Role.ADMIN) {
			return NextResponse.json(
				{ 
					success: false, 
					error: 'Access denied. Admin privileges required.' 
				},
				{ status: 403 }
			);
		}

		// Check if account is disabled
		if (user.disabled) {
			return NextResponse.json(
				{ 
					success: false, 
					error: 'Account is disabled. Please contact system administrator.' 
				},
				{ status: 403 }
			);
		}

		// Generate JWT token
		const token = await generateToken(user);
		if (!token) {
			throw new Error('Token generation failed');
		}

		// Set secure cookie
		const cookieStore = await cookies();
		const authConfig = await getAuthConfig();
		const serverConfig = await getServerConfig();
		
		cookieStore.set({
			name: authConfig.jwtCookieName,
			value: token,
			httpOnly: true,
			secure: serverConfig.env === 'production',
			sameSite: 'strict',
			path: '/',
			maxAge: authConfig.jwtExpiresIn,
		});

		return NextResponse.json({
			success: true,
			message: 'Admin login successful',
			data: {
				user: {
					id: user.id,
					username: user.username,
					role: user.role,
				},
			},
		});
	} catch (error) {
		console.error('Admin login error:', error);
		
		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{ 
					success: false, 
					error: 'Validation error',
					details: error.errors
				},
				{ status: 400 }
			);
		}

		if (error instanceof Error && error.message === 'Invalid password') {
			return NextResponse.json(
				{ 
					success: false, 
					error: 'Invalid username or password' 
				},
				{ status: 401 }
			);
		}

		return NextResponse.json(
			{ 
				success: false, 
				error: 'Login failed',
				details: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
}

/**
 * DELETE /api/admin/auth
 * Admin logout endpoint
 */
async function DELETE(request: NextRequest) {
	try {
		const cookieStore = await cookies();
		const authConfig = await getAuthConfig();
		
		// Clear the auth cookie
		cookieStore.delete(authConfig.jwtCookieName);

		return NextResponse.json({
			success: true,
			message: 'Logout successful',
		});
	} catch (error) {
		console.error('Admin logout error:', error);
		return NextResponse.json(
			{ 
				success: false, 
				error: 'Logout failed',
				details: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
}

// Apply error handling middleware (no auth required for login)
export { 
	withErrorHandling(POST) as POST,
	withErrorHandling(DELETE) as DELETE
};
