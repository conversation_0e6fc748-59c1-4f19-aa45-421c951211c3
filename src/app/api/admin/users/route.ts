import { NextRequest, NextResponse } from 'next/server';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { withAdminAuth } from '@/backend/middleware/admin.middleware';
import { getAdminService } from '@/backend/wire';
import { Role } from '@prisma/client';
import { z } from 'zod';

// Validation schemas
const getUsersQuerySchema = z.object({
	page: z
		.string()
		.optional()
		.transform((val) => (val ? parseInt(val, 10) : 1)),
	limit: z
		.string()
		.optional()
		.transform((val) => (val ? parseInt(val, 10) : 50)),
	search: z.string().optional(),
});

const updateUserSchema = z.object({
	userId: z.string().uuid(),
	role: z.nativeEnum(Role).optional(),
	disabled: z.boolean().optional(),
});

const createAdminSchema = z.object({
	username: z.string().min(3).max(50),
	password: z.string().min(6),
});

/**
 * GET /api/admin/users
 * Get paginated list of all users with filtering
 */
async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const query = getUsersQuerySchema.parse({
			page: searchParams.get('page'),
			limit: searchParams.get('limit'),
			search: searchParams.get('search'),
		});

		const adminService = getAdminService();
		const result = await adminService.getAllUsers(query.page, query.limit, query.search);

		return NextResponse.json({
			success: true,
			data: result,
		});
	} catch (error) {
		console.error('Error fetching users:', error);
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to fetch users',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

/**
 * POST /api/admin/users
 * Create new admin user
 */
async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const { username, password } = createAdminSchema.parse(body);

		const adminService = getAdminService();
		const newAdmin = await adminService.createAdminUser(username, password);

		return NextResponse.json(
			{
				success: true,
				data: {
					id: newAdmin.id,
					username: newAdmin.username,
					role: newAdmin.role,
					created_at: newAdmin.created_at,
				},
			},
			{ status: 201 }
		);
	} catch (error) {
		console.error('Error creating admin user:', error);

		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{
					success: false,
					error: 'Validation error',
					details: error.errors,
				},
				{ status: 400 }
			);
		}

		return NextResponse.json(
			{
				success: false,
				error: 'Failed to create admin user',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

/**
 * PATCH /api/admin/users
 * Update user role or status
 */
async function PATCH(request: NextRequest) {
	try {
		const body = await request.json();
		const { userId, role, disabled } = updateUserSchema.parse(body);

		const adminService = getAdminService();
		let updatedUser;

		if (role !== undefined) {
			updatedUser = await adminService.updateUserRole(userId, role);
		}

		if (disabled !== undefined) {
			if (disabled) {
				updatedUser = await adminService.disableUser(userId);
			} else {
				updatedUser = await adminService.enableUser(userId);
			}
		}

		return NextResponse.json({
			success: true,
			data: updatedUser,
		});
	} catch (error) {
		console.error('Error updating user:', error);

		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{
					success: false,
					error: 'Validation error',
					details: error.errors,
				},
				{ status: 400 }
			);
		}

		return NextResponse.json(
			{
				success: false,
				error: 'Failed to update user',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

// Apply admin authentication middleware
const wrappedGET = withAdminAuth(withErrorHandling(GET));
const wrappedPOST = withAdminAuth(withErrorHandling(POST));
const wrappedPATCH = withAdminAuth(withErrorHandling(PATCH));

export { wrappedGET as GET, wrappedPOST as POST, wrappedPATCH as PATCH };
