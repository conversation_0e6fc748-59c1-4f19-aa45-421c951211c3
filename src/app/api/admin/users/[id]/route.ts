import { NextRequest, NextResponse } from 'next/server';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { withAdminAuth } from '@/backend/middleware/admin.middleware';
import { getAdminService } from '@/backend/wire';

/**
 * GET /api/admin/users/[id]
 * Get specific user details by ID
 */
async function GET(
	request: NextRequest,
	{ params }: { params: { id: string } }
) {
	try {
		const { id } = params;

		if (!id) {
			return NextResponse.json(
				{ success: false, error: 'User ID is required' },
				{ status: 400 }
			);
		}

		const adminService = getAdminService();
		const user = await adminService.getUserById(id);

		if (!user) {
			return NextResponse.json(
				{ success: false, error: 'User not found' },
				{ status: 404 }
			);
		}

		// Remove sensitive information
		const { password_hash, ...safeUser } = user;

		return NextResponse.json({
			success: true,
			data: safeUser,
		});
	} catch (error) {
		console.error('Error fetching user:', error);
		return NextResponse.json(
			{ 
				success: false, 
				error: 'Failed to fetch user',
				details: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
}

// Apply admin authentication middleware
export { withAdminAuth(withErrorHandling(GET)) as GET };
