import { NextRequest, NextResponse } from 'next/server';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { withAdminAuth } from '@/backend/middleware/admin.middleware';
import { getAdminService } from '@/backend/wire';
import { z } from 'zod';

// Validation schemas
const getCollectionsQuerySchema = z.object({
	page: z
		.string()
		.optional()
		.transform((val) => (val ? parseInt(val, 10) : 1)),
	limit: z
		.string()
		.optional()
		.transform((val) => (val ? parseInt(val, 10) : 50)),
});

const deleteCollectionSchema = z.object({
	collectionId: z.string().uuid(),
});

/**
 * GET /api/admin/collections
 * Get paginated list of all collections
 */
async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const query = getCollectionsQuerySchema.parse({
			page: searchParams.get('page'),
			limit: searchParams.get('limit'),
		});

		const adminService = getAdminService();
		const result = await adminService.getAllCollections(query.page, query.limit);

		return NextResponse.json({
			success: true,
			data: result,
		});
	} catch (error) {
		console.error('Error fetching collections:', error);
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to fetch collections',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

/**
 * DELETE /api/admin/collections
 * Delete a collection by ID
 */
async function DELETE(request: NextRequest) {
	try {
		const body = await request.json();
		const { collectionId } = deleteCollectionSchema.parse(body);

		const adminService = getAdminService();
		await adminService.deleteCollection(collectionId);

		return NextResponse.json({
			success: true,
			message: 'Collection deleted successfully',
		});
	} catch (error) {
		console.error('Error deleting collection:', error);

		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{
					success: false,
					error: 'Validation error',
					details: error.errors,
				},
				{ status: 400 }
			);
		}

		return NextResponse.json(
			{
				success: false,
				error: 'Failed to delete collection',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

// Apply admin authentication middleware
const wrappedGET = withAdminAuth(withErrorHandling(GET));
const wrappedDELETE = withAdminAuth(withErrorHandling(DELETE));

export { wrappedGET as GET, wrappedDELETE as DELETE };
