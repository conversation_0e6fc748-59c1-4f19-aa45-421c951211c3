import { NextRequest, NextResponse } from 'next/server';
import { authMiddleware } from './backend/middleware/auth.middleware';

// Ensure middleware only runs on API routes
export const config = {
	matcher: [
		// Match all API routes
		'/api/:path*',
		// Match admin pages
		'/admin/:path*',
	],
};

export async function middleware(request: NextRequest) {
	// Skip authentication for public endpoints
	const publicPaths = [
		'/api/auth/telegram-login',
		'/api/auth/logout',
		'/api/auth/login',
		'/api/auth/google-login',
		'/api/admin/auth', // Admin login endpoint
	];

	// Admin page protection
	if (request.nextUrl.pathname.startsWith('/admin')) {
		// Allow admin login page
		if (request.nextUrl.pathname === '/admin/login') {
			return NextResponse.next();
		}

		// Check for admin authentication
		const jwtCookieName = process.env.JWT_COOKIE_NAME || 'auth_token';
		const token = request.cookies.get(jwtCookieName)?.value;
		if (!token) {
			return NextResponse.redirect(new URL('/admin/login', request.url));
		}
	}

	// Only apply auth middleware for URLs starting with /api
	// except for the public endpoints
	if (
		request.nextUrl.pathname.startsWith('/api') &&
		!publicPaths.some((path) => request.nextUrl.pathname.startsWith(path))
	) {
		const result = await authMiddleware(request);

		// If result is a Response, it means there was an error
		if (result instanceof Response) {
			return result;
		}
	}

	const response = NextResponse.next({
		request: {
			// New request headers
			headers: request.headers,
		},
	});

	return response;
}
