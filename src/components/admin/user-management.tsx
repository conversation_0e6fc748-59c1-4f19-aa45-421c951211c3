'use client';

import { useState, useEffect } from 'react';
import {
	<PERSON><PERSON>,
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	Input,
	LoadingSpinner,
	Badge,
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui';
import { useToast } from '@/contexts/toast-context';
import {
	Users,
	Search,
	RefreshCw,
	UserPlus,
	Edit,
	Ban,
	CheckCircle,
	Shield,
	User,
} from 'lucide-react';
import { Role } from '@prisma/client';

interface UserData {
	id: string;
	username: string | null;
	provider: string;
	provider_id: string;
	role: Role;
	disabled: boolean;
	created_at: string;
}

interface UsersResponse {
	users: UserData[];
	total: number;
	page: number;
	limit: number;
}

export function UserManagement() {
	const [users, setUsers] = useState<UserData[]>([]);
	const [loading, setLoading] = useState(true);
	const [refreshing, setRefreshing] = useState(false);
	const [searchTerm, setSearchTerm] = useState('');
	const [currentPage, setCurrentPage] = useState(1);
	const [totalUsers, setTotalUsers] = useState(0);
	const [updatingUsers, setUpdatingUsers] = useState<Set<string>>(new Set());
	const { showSuccess, showError } = useToast();

	const limit = 20;

	const fetchUsers = async (page = 1, search = '', isRefresh = false) => {
		if (isRefresh) {
			setRefreshing(true);
		} else {
			setLoading(true);
		}

		try {
			const params = new URLSearchParams({
				page: page.toString(),
				limit: limit.toString(),
			});

			if (search.trim()) {
				params.append('search', search.trim());
			}

			const response = await fetch(`/api/admin/users?${params}`);
			
			if (!response.ok) {
				throw new Error('Failed to fetch users');
			}

			const data = await response.json();
			
			if (data.success) {
				setUsers(data.data.users);
				setTotalUsers(data.data.total);
				setCurrentPage(data.data.page);
			} else {
				throw new Error(data.error || 'Failed to fetch users');
			}
		} catch (error) {
			showError(new Error('Failed to load users'));
		} finally {
			setLoading(false);
			setRefreshing(false);
		}
	};

	useEffect(() => {
		fetchUsers(currentPage, searchTerm);
	}, [currentPage]);

	const handleSearch = () => {
		setCurrentPage(1);
		fetchUsers(1, searchTerm);
	};

	const handleRefresh = () => {
		fetchUsers(currentPage, searchTerm, true);
	};

	const updateUserRole = async (userId: string, newRole: Role) => {
		setUpdatingUsers(prev => new Set(prev).add(userId));
		
		try {
			const response = await fetch('/api/admin/users', {
				method: 'PATCH',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					userId,
					role: newRole,
				}),
			});

			const data = await response.json();

			if (response.ok && data.success) {
				setUsers(prev => 
					prev.map(user => 
						user.id === userId 
							? { ...user, role: newRole }
							: user
					)
				);
				showSuccess(`User role updated to ${newRole}`);
			} else {
				throw new Error(data.error || 'Failed to update user role');
			}
		} catch (error) {
			showError(new Error('Failed to update user role'));
		} finally {
			setUpdatingUsers(prev => {
				const newSet = new Set(prev);
				newSet.delete(userId);
				return newSet;
			});
		}
	};

	const toggleUserStatus = async (userId: string, currentDisabled: boolean) => {
		setUpdatingUsers(prev => new Set(prev).add(userId));
		
		try {
			const response = await fetch('/api/admin/users', {
				method: 'PATCH',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					userId,
					disabled: !currentDisabled,
				}),
			});

			const data = await response.json();

			if (response.ok && data.success) {
				setUsers(prev => 
					prev.map(user => 
						user.id === userId 
							? { ...user, disabled: !currentDisabled }
							: user
					)
				);
				showSuccess(`User ${!currentDisabled ? 'disabled' : 'enabled'} successfully`);
			} else {
				throw new Error(data.error || 'Failed to update user status');
			}
		} catch (error) {
			showError(new Error('Failed to update user status'));
		} finally {
			setUpdatingUsers(prev => {
				const newSet = new Set(prev);
				newSet.delete(userId);
				return newSet;
			});
		}
	};

	const getRoleBadgeColor = (role: Role) => {
		switch (role) {
			case Role.ADMIN:
				return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
			case Role.USER:
				return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
			default:
				return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
		}
	};

	const getStatusBadgeColor = (disabled: boolean) => {
		return disabled
			? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
			: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
	};

	const totalPages = Math.ceil(totalUsers / limit);

	if (loading) {
		return (
			<div className="flex items-center justify-center h-64">
				<LoadingSpinner className="h-8 w-8" />
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<div>
					<h1 className="text-3xl font-bold text-gray-900 dark:text-white">
						User Management
					</h1>
					<p className="text-gray-600 dark:text-gray-400 mt-1">
						Manage user accounts and permissions
					</p>
				</div>
				<div className="flex space-x-2">
					<Button
						onClick={handleRefresh}
						disabled={refreshing}
						variant="outline"
					>
						<RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
						Refresh
					</Button>
				</div>
			</div>

			{/* Search and Filters */}
			<Card>
				<CardContent className="pt-6">
					<div className="flex space-x-4">
						<div className="flex-1">
							<Input
								placeholder="Search users..."
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
							/>
						</div>
						<Button onClick={handleSearch}>
							<Search className="h-4 w-4 mr-2" />
							Search
						</Button>
					</div>
				</CardContent>
			</Card>

			{/* Users Table */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center">
						<Users className="h-5 w-5 mr-2" />
						Users ({totalUsers})
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="overflow-x-auto">
						<table className="w-full">
							<thead>
								<tr className="border-b border-gray-200 dark:border-gray-700">
									<th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
										User
									</th>
									<th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
										Provider
									</th>
									<th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
										Role
									</th>
									<th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
										Status
									</th>
									<th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
										Created
									</th>
									<th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
										Actions
									</th>
								</tr>
							</thead>
							<tbody>
								{users.map((user) => (
									<tr
										key={user.id}
										className="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800"
									>
										<td className="py-3 px-4">
											<div className="flex items-center">
												{user.role === Role.ADMIN ? (
													<Shield className="h-4 w-4 text-red-500 mr-2" />
												) : (
													<User className="h-4 w-4 text-gray-400 mr-2" />
												)}
												<span className="font-medium">
													{user.username || 'Anonymous'}
												</span>
											</div>
										</td>
										<td className="py-3 px-4">
											<Badge variant="outline">
												{user.provider}
											</Badge>
										</td>
										<td className="py-3 px-4">
											<Select
												value={user.role}
												onValueChange={(value: Role) => updateUserRole(user.id, value)}
												disabled={updatingUsers.has(user.id)}
											>
												<SelectTrigger className="w-32">
													<SelectValue />
												</SelectTrigger>
												<SelectContent>
													<SelectItem value={Role.USER}>User</SelectItem>
													<SelectItem value={Role.ADMIN}>Admin</SelectItem>
												</SelectContent>
											</Select>
										</td>
										<td className="py-3 px-4">
											<Badge className={getStatusBadgeColor(user.disabled)}>
												{user.disabled ? 'Disabled' : 'Active'}
											</Badge>
										</td>
										<td className="py-3 px-4 text-sm text-gray-600 dark:text-gray-400">
											{new Date(user.created_at).toLocaleDateString()}
										</td>
										<td className="py-3 px-4">
											<Button
												size="sm"
												variant={user.disabled ? "default" : "destructive"}
												onClick={() => toggleUserStatus(user.id, user.disabled)}
												disabled={updatingUsers.has(user.id)}
											>
												{updatingUsers.has(user.id) ? (
													<LoadingSpinner className="h-3 w-3" />
												) : user.disabled ? (
													<CheckCircle className="h-3 w-3" />
												) : (
													<Ban className="h-3 w-3" />
												)}
											</Button>
										</td>
									</tr>
								))}
							</tbody>
						</table>
					</div>

					{/* Pagination */}
					{totalPages > 1 && (
						<div className="flex justify-between items-center mt-6">
							<div className="text-sm text-gray-600 dark:text-gray-400">
								Showing {(currentPage - 1) * limit + 1} to {Math.min(currentPage * limit, totalUsers)} of {totalUsers} users
							</div>
							<div className="flex space-x-2">
								<Button
									variant="outline"
									size="sm"
									onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
									disabled={currentPage === 1}
								>
									Previous
								</Button>
								<Button
									variant="outline"
									size="sm"
									onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
									disabled={currentPage === totalPages}
								>
									Next
								</Button>
							</div>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
