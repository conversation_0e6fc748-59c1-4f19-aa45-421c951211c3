'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
	<PERSON><PERSON>,
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	Input,
	LoadingSpinner,
} from '@/components/ui';
import { useToast } from '@/contexts/toast-context';
import { Shield, Eye, EyeOff } from 'lucide-react';

export function AdminLogin() {
	const [username, setUsername] = useState('');
	const [password, setPassword] = useState('');
	const [showPassword, setShowPassword] = useState(false);
	const [loading, setLoading] = useState(false);
	const router = useRouter();
	const { showSuccess, showError } = useToast();

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!username.trim() || !password.trim()) {
			showError(new Error('Please enter both username and password'));
			return;
		}

		setLoading(true);
		try {
			const response = await fetch('/api/admin/auth', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					username: username.trim(),
					password,
				}),
			});

			const data = await response.json();

			if (response.ok && data.success) {
				showSuccess('Login successful');
				router.push('/admin');
			} else {
				showError(new Error(data.error || 'Login failed'));
			}
		} catch (error) {
			showError(new Error('Login failed. Please try again.'));
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
			<Card className="w-full max-w-md">
				<CardHeader className="text-center">
					<div className="flex justify-center mb-4">
						<div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
							<Shield className="h-8 w-8 text-blue-600 dark:text-blue-400" />
						</div>
					</div>
					<CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
						Admin Login
					</CardTitle>
					<p className="text-gray-600 dark:text-gray-400 mt-2">
						Sign in to access the admin dashboard
					</p>
				</CardHeader>
				<CardContent>
					<form onSubmit={handleSubmit} className="space-y-4">
						<div>
							<label
								htmlFor="username"
								className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
							>
								Username
							</label>
							<Input
								id="username"
								type="text"
								value={username}
								onChange={(e) => setUsername(e.target.value)}
								placeholder="Enter your admin username"
								disabled={loading}
								required
								autoComplete="username"
								className="w-full"
							/>
						</div>

						<div>
							<label
								htmlFor="password"
								className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
							>
								Password
							</label>
							<div className="relative">
								<Input
									id="password"
									type={showPassword ? 'text' : 'password'}
									value={password}
									onChange={(e) => setPassword(e.target.value)}
									placeholder="Enter your password"
									disabled={loading}
									required
									autoComplete="current-password"
									className="w-full pr-10"
								/>
								<Button
									type="button"
									variant="ghost"
									size="sm"
									className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
									onClick={() => setShowPassword(!showPassword)}
									disabled={loading}
									aria-label={showPassword ? 'Hide password' : 'Show password'}
									data-testid="password-toggle"
								>
									{showPassword ? (
										<EyeOff className="h-4 w-4 text-gray-400" />
									) : (
										<Eye className="h-4 w-4 text-gray-400" />
									)}
								</Button>
							</div>
						</div>

						<Button type="submit" className="w-full" disabled={loading}>
							{loading ? (
								<>
									<LoadingSpinner className="h-4 w-4 mr-2" />
									Signing in...
								</>
							) : (
								'Sign In'
							)}
						</Button>
					</form>

					<div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
						<p className="text-sm text-yellow-800 dark:text-yellow-200">
							<strong>Default Credentials:</strong>
						</p>
						<p className="text-xs text-yellow-700 dark:text-yellow-300 mt-1">
							Username: admin | Password: admin123
						</p>
						<p className="text-xs text-yellow-700 dark:text-yellow-300">
							Username: superadmin | Password: superadmin123
						</p>
						<p className="text-xs text-yellow-600 dark:text-yellow-400 mt-2">
							⚠️ Change these passwords in production!
						</p>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
