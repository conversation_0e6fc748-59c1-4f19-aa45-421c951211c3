'use client';

import { useState, useEffect } from 'react';
import {
	<PERSON>,
	<PERSON>Content,
	Card<PERSON>eader,
	Card<PERSON>itle,
	Loading<PERSON><PERSON>ner,
	<PERSON><PERSON>,
	<PERSON><PERSON>,
} from '@/components/ui';
import { useToast } from '@/contexts/toast-context';
import {
	Users,
	FolderOpen,
	MessageSquare,
	Activity,
	TrendingUp,
	TrendingDown,
	AlertTriangle,
	CheckCircle,
	RefreshCw,
} from 'lucide-react';

interface AdminStats {
	users: {
		total: number;
		active: number;
		admins: number;
		newThisWeek: number;
	};
	collections: {
		total: number;
		public: number;
		private: number;
	};
	words: {
		total: number;
		byLanguage: Record<string, number>;
	};
	feedback: {
		total: number;
		pending: number;
		resolved: number;
	};
	system: {
		cacheHitRate: number;
		apiResponseTime: number;
		errorRate: number;
	};
}

interface SystemHealth {
	status: string;
	details: Record<string, any>;
}

export function AdminDashboardOverview() {
	const [stats, setStats] = useState<AdminStats | null>(null);
	const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
	const [loading, setLoading] = useState(true);
	const [refreshing, setRefreshing] = useState(false);
	const { showError } = useToast();

	const fetchDashboardData = async (isRefresh = false) => {
		if (isRefresh) {
			setRefreshing(true);
		} else {
			setLoading(true);
		}

		try {
			const response = await fetch('/api/admin/dashboard', {
				credentials: 'include',
			});

			if (!response.ok) {
				throw new Error('Failed to fetch dashboard data');
			}

			const data = await response.json();

			if (data.success) {
				setStats(data.data.stats);
				setSystemHealth(data.data.systemHealth);
			} else {
				throw new Error(data.error || 'Failed to fetch dashboard data');
			}
		} catch (error) {
			showError(new Error('Failed to load dashboard data'));
		} finally {
			setLoading(false);
			setRefreshing(false);
		}
	};

	useEffect(() => {
		fetchDashboardData();
	}, []);

	const handleRefresh = () => {
		fetchDashboardData(true);
	};

	if (loading) {
		return (
			<div className="flex items-center justify-center h-64">
				<LoadingSpinner className="h-8 w-8" />
			</div>
		);
	}

	if (!stats || !systemHealth) {
		return (
			<div className="text-center py-12">
				<AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
				<p className="text-gray-600 dark:text-gray-400">Failed to load dashboard data</p>
				<Button onClick={handleRefresh} className="mt-4">
					Try Again
				</Button>
			</div>
		);
	}

	const getHealthStatusColor = (status: string) => {
		switch (status.toLowerCase()) {
			case 'healthy':
				return 'text-green-600 bg-green-100 dark:bg-green-900';
			case 'warning':
				return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900';
			case 'error':
				return 'text-red-600 bg-red-100 dark:bg-red-900';
			default:
				return 'text-gray-600 bg-gray-100 dark:bg-gray-900';
		}
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<div>
					<h1 className="text-3xl font-bold text-gray-900 dark:text-white">
						Admin Dashboard
					</h1>
					<p className="text-gray-600 dark:text-gray-400 mt-1">
						System overview and key metrics
					</p>
				</div>
				<Button onClick={handleRefresh} disabled={refreshing} variant="outline">
					<RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
					Refresh
				</Button>
			</div>

			{/* System Health */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center">
						<Activity className="h-5 w-5 mr-2" />
						System Health
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="flex items-center space-x-4">
						<Badge className={getHealthStatusColor(systemHealth.status)}>
							{systemHealth.status.toUpperCase()}
						</Badge>
						<div className="flex space-x-6 text-sm">
							<div>
								<span className="text-gray-600 dark:text-gray-400">Database:</span>
								<span className="ml-2 font-medium">
									{systemHealth.details.database}
								</span>
							</div>
							<div>
								<span className="text-gray-600 dark:text-gray-400">Cache:</span>
								<span className="ml-2 font-medium">
									{systemHealth.details.cache}
								</span>
							</div>
							<div>
								<span className="text-gray-600 dark:text-gray-400">
									AI Services:
								</span>
								<span className="ml-2 font-medium">
									{systemHealth.details.ai_services}
								</span>
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Stats Grid */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
				{/* Users Stats */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Total Users</CardTitle>
						<Users className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{stats.users.total}</div>
						<div className="text-xs text-muted-foreground mt-1">
							<span className="text-green-600">+{stats.users.newThisWeek}</span> new
							this week
						</div>
						<div className="text-xs text-muted-foreground mt-2">
							Active: {stats.users.active} | Admins: {stats.users.admins}
						</div>
					</CardContent>
				</Card>

				{/* Collections Stats */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Collections</CardTitle>
						<FolderOpen className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{stats.collections.total}</div>
						<div className="text-xs text-muted-foreground mt-2">
							Public: {stats.collections.public} | Private:{' '}
							{stats.collections.private}
						</div>
					</CardContent>
				</Card>

				{/* Feedback Stats */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Feedback</CardTitle>
						<MessageSquare className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{stats.feedback.total}</div>
						<div className="text-xs text-muted-foreground mt-1">
							<span className="text-yellow-600">{stats.feedback.pending}</span>{' '}
							pending
						</div>
						<div className="text-xs text-muted-foreground">
							<span className="text-green-600">{stats.feedback.resolved}</span>{' '}
							resolved
						</div>
					</CardContent>
				</Card>

				{/* System Performance */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Performance</CardTitle>
						<TrendingUp className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{stats.system.apiResponseTime}ms</div>
						<div className="text-xs text-muted-foreground mt-1">
							Cache Hit: {(stats.system.cacheHitRate * 100).toFixed(1)}%
						</div>
						<div className="text-xs text-muted-foreground">
							Error Rate: {(stats.system.errorRate * 100).toFixed(2)}%
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
