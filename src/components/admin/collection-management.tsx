'use client';

import { useState, useEffect } from 'react';
import {
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON>ontent,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
} from '@/components/ui';
import { useToast } from '@/contexts/toast-context';
import {
	<PERSON>olderO<PERSON>,
	RefreshCw,
	Trash2,
	Eye,
	Calendar,
	User,
} from 'lucide-react';
import { Language } from '@prisma/client';

interface CollectionData {
	id: string;
	name: string;
	target_language: Language;
	source_language: Language;
	user_id: string;
	word_ids: string[];
	paragraph_ids: string[];
	keyword_ids: string[];
	created_at: string;
	updated_at: string;
}

interface CollectionsResponse {
	collections: CollectionData[];
	total: number;
	page: number;
	limit: number;
}

export function CollectionManagement() {
	const [collections, setCollections] = useState<CollectionData[]>([]);
	const [loading, setLoading] = useState(true);
	const [refreshing, setRefreshing] = useState(false);
	const [currentPage, setCurrentPage] = useState(1);
	const [totalCollections, setTotalCollections] = useState(0);
	const [deletingCollections, setDeletingCollections] = useState<Set<string>>(new Set());
	const { showSuccess, showError } = useToast();

	const limit = 20;

	const fetchCollections = async (page = 1, isRefresh = false) => {
		if (isRefresh) {
			setRefreshing(true);
		} else {
			setLoading(true);
		}

		try {
			const params = new URLSearchParams({
				page: page.toString(),
				limit: limit.toString(),
			});

			const response = await fetch(`/api/admin/collections?${params}`);
			
			if (!response.ok) {
				throw new Error('Failed to fetch collections');
			}

			const data = await response.json();
			
			if (data.success) {
				setCollections(data.data.collections);
				setTotalCollections(data.data.total);
				setCurrentPage(data.data.page);
			} else {
				throw new Error(data.error || 'Failed to fetch collections');
			}
		} catch (error) {
			showError(new Error('Failed to load collections'));
		} finally {
			setLoading(false);
			setRefreshing(false);
		}
	};

	useEffect(() => {
		fetchCollections(currentPage);
	}, [currentPage]);

	const handleRefresh = () => {
		fetchCollections(currentPage, true);
	};

	const deleteCollection = async (collectionId: string, collectionName: string) => {
		if (!confirm(`Are you sure you want to delete the collection "${collectionName}"? This action cannot be undone.`)) {
			return;
		}

		setDeletingCollections(prev => new Set(prev).add(collectionId));
		
		try {
			const response = await fetch('/api/admin/collections', {
				method: 'DELETE',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					collectionId,
				}),
			});

			const data = await response.json();

			if (response.ok && data.success) {
				setCollections(prev => prev.filter(collection => collection.id !== collectionId));
				setTotalCollections(prev => prev - 1);
				showSuccess('Collection deleted successfully');
			} else {
				throw new Error(data.error || 'Failed to delete collection');
			}
		} catch (error) {
			showError(new Error('Failed to delete collection'));
		} finally {
			setDeletingCollections(prev => {
				const newSet = new Set(prev);
				newSet.delete(collectionId);
				return newSet;
			});
		}
	};

	const getLanguageBadgeColor = (language: Language) => {
		switch (language) {
			case Language.EN:
				return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
			case Language.VI:
				return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
			default:
				return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
		}
	};

	const totalPages = Math.ceil(totalCollections / limit);

	if (loading) {
		return (
			<div className="flex items-center justify-center h-64">
				<LoadingSpinner className="h-8 w-8" />
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<div>
					<h1 className="text-3xl font-bold text-gray-900 dark:text-white">
						Collection Management
					</h1>
					<p className="text-gray-600 dark:text-gray-400 mt-1">
						Manage user collections and content
					</p>
				</div>
				<Button
					onClick={handleRefresh}
					disabled={refreshing}
					variant="outline"
				>
					<RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
					Refresh
				</Button>
			</div>

			{/* Collections Grid */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
				{collections.map((collection) => (
					<Card key={collection.id} className="hover:shadow-lg transition-shadow">
						<CardHeader>
							<CardTitle className="flex items-center justify-between">
								<div className="flex items-center">
									<FolderOpen className="h-5 w-5 mr-2 text-blue-600" />
									<span className="truncate">{collection.name}</span>
								</div>
								<div className="flex space-x-1">
									<Button
										size="sm"
										variant="outline"
										onClick={() => {
											// TODO: Implement view collection details
											showSuccess('View collection details - Coming soon');
										}}
									>
										<Eye className="h-3 w-3" />
									</Button>
									<Button
										size="sm"
										variant="destructive"
										onClick={() => deleteCollection(collection.id, collection.name)}
										disabled={deletingCollections.has(collection.id)}
									>
										{deletingCollections.has(collection.id) ? (
											<LoadingSpinner className="h-3 w-3" />
										) : (
											<Trash2 className="h-3 w-3" />
										)}
									</Button>
								</div>
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="space-y-3">
								{/* Languages */}
								<div className="flex items-center space-x-2">
									<Badge className={getLanguageBadgeColor(collection.source_language)}>
										{collection.source_language}
									</Badge>
									<span className="text-gray-400">→</span>
									<Badge className={getLanguageBadgeColor(collection.target_language)}>
										{collection.target_language}
									</Badge>
								</div>

								{/* Content Stats */}
								<div className="grid grid-cols-3 gap-2 text-sm">
									<div className="text-center p-2 bg-gray-50 dark:bg-gray-800 rounded">
										<div className="font-semibold text-blue-600">
											{collection.word_ids.length}
										</div>
										<div className="text-gray-600 dark:text-gray-400">Words</div>
									</div>
									<div className="text-center p-2 bg-gray-50 dark:bg-gray-800 rounded">
										<div className="font-semibold text-green-600">
											{collection.paragraph_ids.length}
										</div>
										<div className="text-gray-600 dark:text-gray-400">Paragraphs</div>
									</div>
									<div className="text-center p-2 bg-gray-50 dark:bg-gray-800 rounded">
										<div className="font-semibold text-purple-600">
											{collection.keyword_ids.length}
										</div>
										<div className="text-gray-600 dark:text-gray-400">Keywords</div>
									</div>
								</div>

								{/* Metadata */}
								<div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
									<div className="flex items-center">
										<User className="h-3 w-3 mr-1" />
										<span>User ID: {collection.user_id.slice(0, 8)}...</span>
									</div>
									<div className="flex items-center">
										<Calendar className="h-3 w-3 mr-1" />
										<span>Created: {new Date(collection.created_at).toLocaleDateString()}</span>
									</div>
								</div>
							</div>
						</CardContent>
					</Card>
				))}
			</div>

			{/* Empty State */}
			{collections.length === 0 && (
				<Card>
					<CardContent className="text-center py-12">
						<FolderOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
						<p className="text-gray-600 dark:text-gray-400">
							No collections found
						</p>
					</CardContent>
				</Card>
			)}

			{/* Pagination */}
			{totalPages > 1 && (
				<div className="flex justify-between items-center">
					<div className="text-sm text-gray-600 dark:text-gray-400">
						Showing {(currentPage - 1) * limit + 1} to {Math.min(currentPage * limit, totalCollections)} of {totalCollections} collections
					</div>
					<div className="flex space-x-2">
						<Button
							variant="outline"
							size="sm"
							onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
							disabled={currentPage === 1}
						>
							Previous
						</Button>
						<Button
							variant="outline"
							size="sm"
							onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
							disabled={currentPage === totalPages}
						>
							Next
						</Button>
					</div>
				</div>
			)}
		</div>
	);
}
