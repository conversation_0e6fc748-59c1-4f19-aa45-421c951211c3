import { UserRepository } from '@/backend/repositories';
import { Prisma } from '@prisma/client';

export interface AuditEvent {
	id: string;
	action: string;
	resource: string;
	resource_id?: string;
	user_id?: string;
	admin_id?: string;
	details: Record<string, any>;
	ip_address?: string;
	user_agent?: string;
	timestamp: Date;
}

export interface AuditLogQuery {
	action?: string;
	resource?: string;
	user_id?: string;
	admin_id?: string;
	start_date?: Date;
	end_date?: Date;
	limit?: number;
	offset?: number;
}

export interface AuditService {
	logEvent(event: Omit<AuditEvent, 'id' | 'timestamp'>): Promise<void>;
	getAuditLogs(query: AuditLogQuery): Promise<AuditEvent[]>;
	getAuditLogsCount(query: Omit<AuditLogQuery, 'limit' | 'offset'>): Promise<number>;
	getUserActivity(userId: string, limit?: number): Promise<AuditEvent[]>;
	getAdminActivity(adminId: string, limit?: number): Promise<AuditEvent[]>;
	getSystemActivity(limit?: number): Promise<AuditEvent[]>;
}

export class AuditServiceImpl implements AuditService {
	private auditLogs: AuditEvent[] = []; // In-memory storage for now
	private nextId = 1;

	constructor(private readonly getUserRepository: () => UserRepository) {}

	async logEvent(event: Omit<AuditEvent, 'id' | 'timestamp'>): Promise<void> {
		const auditEvent: AuditEvent = {
			...event,
			id: (this.nextId++).toString(),
			timestamp: new Date(),
		};

		// Store in memory (in production, this would go to a database)
		this.auditLogs.push(auditEvent);

		// Keep only the last 1000 events to prevent memory issues
		if (this.auditLogs.length > 1000) {
			this.auditLogs = this.auditLogs.slice(-1000);
		}

		console.log('Audit Event:', auditEvent);
	}

	async getAuditLogs(query: AuditLogQuery): Promise<AuditEvent[]> {
		let filteredLogs = [...this.auditLogs];

		// Apply filters
		if (query.action) {
			filteredLogs = filteredLogs.filter(log => log.action === query.action);
		}

		if (query.resource) {
			filteredLogs = filteredLogs.filter(log => log.resource === query.resource);
		}

		if (query.user_id) {
			filteredLogs = filteredLogs.filter(log => log.user_id === query.user_id);
		}

		if (query.admin_id) {
			filteredLogs = filteredLogs.filter(log => log.admin_id === query.admin_id);
		}

		if (query.start_date) {
			filteredLogs = filteredLogs.filter(log => log.timestamp >= query.start_date!);
		}

		if (query.end_date) {
			filteredLogs = filteredLogs.filter(log => log.timestamp <= query.end_date!);
		}

		// Sort by timestamp (newest first)
		filteredLogs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

		// Apply pagination
		const offset = query.offset || 0;
		const limit = query.limit || 50;
		
		return filteredLogs.slice(offset, offset + limit);
	}

	async getAuditLogsCount(query: Omit<AuditLogQuery, 'limit' | 'offset'>): Promise<number> {
		const logs = await this.getAuditLogs({ ...query, limit: 10000 });
		return logs.length;
	}

	async getUserActivity(userId: string, limit: number = 50): Promise<AuditEvent[]> {
		return this.getAuditLogs({ user_id: userId, limit });
	}

	async getAdminActivity(adminId: string, limit: number = 50): Promise<AuditEvent[]> {
		return this.getAuditLogs({ admin_id: adminId, limit });
	}

	async getSystemActivity(limit: number = 50): Promise<AuditEvent[]> {
		return this.getAuditLogs({ limit });
	}
}

// Audit event types
export const AUDIT_ACTIONS = {
	// User actions
	USER_LOGIN: 'user_login',
	USER_LOGOUT: 'user_logout',
	USER_CREATED: 'user_created',
	USER_UPDATED: 'user_updated',
	USER_DISABLED: 'user_disabled',
	USER_ENABLED: 'user_enabled',
	USER_ROLE_CHANGED: 'user_role_changed',

	// Admin actions
	ADMIN_LOGIN: 'admin_login',
	ADMIN_LOGOUT: 'admin_logout',
	ADMIN_USER_CREATED: 'admin_user_created',
	ADMIN_USER_UPDATED: 'admin_user_updated',
	ADMIN_USER_DISABLED: 'admin_user_disabled',
	ADMIN_USER_ENABLED: 'admin_user_enabled',
	ADMIN_COLLECTION_DELETED: 'admin_collection_deleted',
	ADMIN_CACHE_CLEARED: 'admin_cache_cleared',

	// Collection actions
	COLLECTION_CREATED: 'collection_created',
	COLLECTION_UPDATED: 'collection_updated',
	COLLECTION_DELETED: 'collection_deleted',

	// Word actions
	WORD_CREATED: 'word_created',
	WORD_UPDATED: 'word_updated',
	WORD_DELETED: 'word_deleted',

	// Feedback actions
	FEEDBACK_CREATED: 'feedback_created',
	FEEDBACK_UPDATED: 'feedback_updated',

	// System actions
	SYSTEM_ERROR: 'system_error',
	SYSTEM_WARNING: 'system_warning',
	CACHE_CLEARED: 'cache_cleared',
} as const;

export const AUDIT_RESOURCES = {
	USER: 'user',
	ADMIN: 'admin',
	COLLECTION: 'collection',
	WORD: 'word',
	FEEDBACK: 'feedback',
	SYSTEM: 'system',
	CACHE: 'cache',
} as const;

// Helper function to create audit events
export function createAuditEvent(
	action: string,
	resource: string,
	details: Record<string, any> = {},
	options: {
		resource_id?: string;
		user_id?: string;
		admin_id?: string;
		ip_address?: string;
		user_agent?: string;
	} = {}
): Omit<AuditEvent, 'id' | 'timestamp'> {
	return {
		action,
		resource,
		details,
		...options,
	};
}
