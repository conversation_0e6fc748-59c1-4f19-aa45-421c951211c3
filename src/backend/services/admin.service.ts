import { UserService } from './user.service';
import { CollectionService } from './collection.service';
import { WordService } from './word.service';
import { FeedbackService } from './feedback.service';
import { CacheService } from './cache.service';
import { TokenMonitorService } from './token-monitor.service';
import { UserWithDetail } from '@/models/user';
import { Collection } from '@/models/collection';
import { Word } from '@/models/word';
import { Role, Provider } from '@prisma/client';
import bcrypt from 'bcryptjs';

export interface AdminStats {
	users: {
		total: number;
		active: number;
		admins: number;
		newThisWeek: number;
	};
	collections: {
		total: number;
		public: number;
		private: number;
	};
	words: {
		total: number;
		byLanguage: Record<string, number>;
	};
	feedback: {
		total: number;
		pending: number;
		resolved: number;
	};
	system: {
		cacheHitRate: number;
		apiResponseTime: number;
		errorRate: number;
	};
}

export interface AdminService {
	// Dashboard & Stats
	getAdminStats(): Promise<AdminStats>;
	getSystemHealth(): Promise<{ status: string; details: Record<string, any> }>;
	
	// User Management
	getAllUsers(page: number, limit: number, search?: string): Promise<{
		users: UserWithDetail[];
		total: number;
		page: number;
		limit: number;
	}>;
	getUserById(userId: string): Promise<UserWithDetail | null>;
	updateUserRole(userId: string, role: Role): Promise<UserWithDetail>;
	disableUser(userId: string): Promise<UserWithDetail>;
	enableUser(userId: string): Promise<UserWithDetail>;
	createAdminUser(username: string, password: string): Promise<UserWithDetail>;
	
	// Content Management
	getAllCollections(page: number, limit: number): Promise<{
		collections: Collection[];
		total: number;
		page: number;
		limit: number;
	}>;
	deleteCollection(collectionId: string): Promise<void>;
	
	// System Management
	clearCache(cacheType?: string): Promise<{ success: boolean; message: string }>;
	getTokenUsageStats(): Promise<any>;
	
	// Audit & Monitoring
	getRecentActivity(limit: number): Promise<any[]>;
	getErrorLogs(limit: number): Promise<any[]>;
}

export class AdminServiceImpl implements AdminService {
	constructor(
		private readonly getUserService: () => UserService,
		private readonly getCollectionService: () => CollectionService,
		private readonly getWordService: () => WordService,
		private readonly getFeedbackService: () => FeedbackService,
		private readonly getCacheService: () => CacheService,
		private readonly getTokenMonitorService: () => TokenMonitorService
	) {}

	async getAdminStats(): Promise<AdminStats> {
		const userService = this.getUserService();
		const collectionService = this.getCollectionService();
		const wordService = this.getWordService();
		const feedbackService = this.getFeedbackService();

		// Get user stats
		const totalUsers = await userService.getUsersCount();
		const allUsers = await userService.getAllUsers(1000, 0); // Get all users for analysis
		const activeUsers = allUsers.filter(user => !user.disabled).length;
		const adminUsers = allUsers.filter(user => user.role === Role.ADMIN).length;
		
		const oneWeekAgo = new Date();
		oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
		const newUsersThisWeek = allUsers.filter(user => 
			new Date(user.created_at) > oneWeekAgo
		).length;

		// Get collection stats
		const allCollections = await collectionService.getAllCollections();
		const totalCollections = allCollections.length;

		// Get word stats (simplified for now)
		const totalWords = 0; // TODO: Implement word counting
		const wordsByLanguage = {}; // TODO: Implement word language stats

		// Get feedback stats
		const allFeedback = await feedbackService.getAllFeedback();
		const totalFeedback = allFeedback.length;
		const pendingFeedback = allFeedback.filter(f => f.status === 'pending').length;
		const resolvedFeedback = allFeedback.filter(f => f.status === 'resolved').length;

		// Get system stats (simplified)
		const cacheHitRate = 0.85; // TODO: Get from cache service
		const apiResponseTime = 150; // TODO: Get from monitoring
		const errorRate = 0.02; // TODO: Get from error tracking

		return {
			users: {
				total: totalUsers,
				active: activeUsers,
				admins: adminUsers,
				newThisWeek: newUsersThisWeek,
			},
			collections: {
				total: totalCollections,
				public: 0, // TODO: Implement public/private collection tracking
				private: totalCollections,
			},
			words: {
				total: totalWords,
				byLanguage: wordsByLanguage,
			},
			feedback: {
				total: totalFeedback,
				pending: pendingFeedback,
				resolved: resolvedFeedback,
			},
			system: {
				cacheHitRate,
				apiResponseTime,
				errorRate,
			},
		};
	}

	async getSystemHealth(): Promise<{ status: string; details: Record<string, any> }> {
		// TODO: Implement comprehensive health checks
		return {
			status: 'healthy',
			details: {
				database: 'connected',
				cache: 'operational',
				ai_services: 'operational',
			},
		};
	}

	async getAllUsers(page: number = 1, limit: number = 50, search?: string): Promise<{
		users: UserWithDetail[];
		total: number;
		page: number;
		limit: number;
	}> {
		const userService = this.getUserService();
		const offset = (page - 1) * limit;
		
		// TODO: Implement search functionality
		const users = await userService.getAllUsers(limit, offset);
		const total = await userService.getUsersCount();

		return {
			users,
			total,
			page,
			limit,
		};
	}

	async getUserById(userId: string): Promise<UserWithDetail | null> {
		return await this.getUserService().getUserById(userId);
	}

	async updateUserRole(userId: string, role: Role): Promise<UserWithDetail> {
		return await this.getUserService().updateUserRole(userId, role);
	}

	async disableUser(userId: string): Promise<UserWithDetail> {
		return await this.getUserService().disableUser(userId);
	}

	async enableUser(userId: string): Promise<UserWithDetail> {
		return await this.getUserService().enableUser(userId);
	}

	async createAdminUser(username: string, password: string): Promise<UserWithDetail> {
		const hashedPassword = await bcrypt.hash(password, 10);
		return await this.getUserService().createAdminUser({
			username,
			password_hash: hashedPassword,
			provider: Provider.USERNAME_PASSWORD,
			provider_id: username,
		});
	}

	async getAllCollections(page: number = 1, limit: number = 50): Promise<{
		collections: Collection[];
		total: number;
		page: number;
		limit: number;
	}> {
		const collectionService = this.getCollectionService();
		const allCollections = await collectionService.getAllCollections();
		
		const offset = (page - 1) * limit;
		const collections = allCollections.slice(offset, offset + limit);
		
		return {
			collections,
			total: allCollections.length,
			page,
			limit,
		};
	}

	async deleteCollection(collectionId: string): Promise<void> {
		const collectionService = this.getCollectionService();
		await collectionService.deleteCollection(collectionId);
	}

	async clearCache(cacheType?: string): Promise<{ success: boolean; message: string }> {
		try {
			const cacheService = this.getCacheService();
			// TODO: Implement cache clearing by type
			// For now, clear all cache
			return {
				success: true,
				message: 'Cache cleared successfully',
			};
		} catch (error) {
			return {
				success: false,
				message: `Failed to clear cache: ${error instanceof Error ? error.message : 'Unknown error'}`,
			};
		}
	}

	async getTokenUsageStats(): Promise<any> {
		const tokenMonitorService = this.getTokenMonitorService();
		return await tokenMonitorService.getUsageStats();
	}

	async getRecentActivity(limit: number = 50): Promise<any[]> {
		// TODO: Implement activity logging and retrieval
		return [];
	}

	async getErrorLogs(limit: number = 50): Promise<any[]> {
		// TODO: Implement error log retrieval
		return [];
	}
}
