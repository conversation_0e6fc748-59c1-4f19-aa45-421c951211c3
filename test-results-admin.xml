<testsuites id="" name="" tests="56" failures="2" skipped="1" errors="0" time="382.90887">
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T16:03:46.639Z" hostname="chromium" tests="8" failures="0" skipped="0" time="26.505" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="1.801">
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="5.61">
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="2.848">
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="1.784">
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="1.683">
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="5.44">
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="3.567">
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="3.772">
</testcase>
</testsuite>
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T16:03:46.639Z" hostname="firefox" tests="8" failures="0" skipped="0" time="58.225" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="2.244">
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="8.893">
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="11.212">
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="6.619">
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="4.083">
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="10.384">
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="7.796">
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="6.994">
</testcase>
</testsuite>
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T16:03:46.639Z" hostname="webkit" tests="8" failures="0" skipped="0" time="35.788" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="3.387">
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="7.896">
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="3.672">
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="3.629">
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="3.622">
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="5.913">
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="4.385">
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="3.284">
</testcase>
</testsuite>
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T16:03:46.639Z" hostname="Mobile Chrome" tests="8" failures="1" skipped="0" time="35.858" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="1.363">
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="6.364">
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="3.696">
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="2.982">
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="2.355">
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="13.424">
<failure message="admin-auth.spec.ts:110:6 should logout successfully" type="FAILURE">
<![CDATA[  [Mobile Chrome] › admin-auth.spec.ts:110:6 › Admin Authentication › should logout successfully ───

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('button').filter({ hasText: 'Logout' })
        - locator resolved to <button id="_r_10_" tabindex="0" role="button" data-slot="button" aria-disabled="false" class="inline-flex items-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:b…>…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
        - retrying click action
        - waiting 20ms
        2 × waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
        - retrying click action
          - waiting 100ms
        18 × waiting for element to be visible, enabled and stable
           - element is visible, enabled and stable
           - scrolling into view if needed
           - done scrolling
           - element is outside of the viewport
         - retrying click action
           - waiting 500ms


      117 | 		// Find and click logout button
      118 | 		const logoutButton = page.locator('button').filter({ hasText: 'Logout' });
    > 119 | 		await logoutButton.click();
          | 		                   ^
      120 |
      121 | 		// Should redirect to login page
      122 | 		await page.waitForURL('/admin/login');
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:119:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Chrome/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Chrome/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="2.807">
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="2.867">
</testcase>
</testsuite>
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T16:03:46.639Z" hostname="Mobile Safari" tests="8" failures="1" skipped="0" time="35.326" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="1.881">
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="5.848">
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="3.387">
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="2.379">
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="1.741">
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="13.689">
<failure message="admin-auth.spec.ts:110:6 should logout successfully" type="FAILURE">
<![CDATA[  [Mobile Safari] › admin-auth.spec.ts:110:6 › Admin Authentication › should logout successfully ───

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('button').filter({ hasText: 'Logout' })
        - locator resolved to <button id="_r_a_" tabindex="0" role="button" data-slot="button" aria-disabled="false" class="inline-flex items-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:bo…>…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
        - retrying click action
        - waiting 20ms
        2 × waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
        - retrying click action
          - waiting 100ms
        17 × waiting for element to be visible, enabled and stable
           - element is visible, enabled and stable
           - scrolling into view if needed
           - done scrolling
           - element is outside of the viewport
         - retrying click action
           - waiting 500ms


      117 | 		// Find and click logout button
      118 | 		const logoutButton = page.locator('button').filter({ hasText: 'Logout' });
    > 119 | 		await logoutButton.click();
          | 		                   ^
      120 |
      121 | 		// Should redirect to login page
      122 | 		await page.waitForURL('/admin/login');
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:119:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Safari/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Safari/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="3.192">
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="3.209">
</testcase>
</testsuite>
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T16:03:46.639Z" hostname="Microsoft Edge" tests="8" failures="0" skipped="0" time="31.921" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="2.461">
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="5.904">
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="3.248">
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="1.921">
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="2.137">
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="6.417">
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="4.536">
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="5.297">
</testcase>
</testsuite>
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T16:03:46.639Z" hostname="Google Chrome" tests="8" failures="0" skipped="1" time="16.487" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="1.725">
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="4.524">
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="3.065">
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="1.442">
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="2.94">
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="1.158">
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="1.633">
</testcase>
</testsuite>
</testsuites>