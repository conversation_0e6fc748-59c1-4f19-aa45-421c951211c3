<testsuites id="" name="" tests="735" failures="70" skipped="598" errors="0" time="300.015645">
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="chromium" tests="8" failures="4" skipped="0" time="59.622" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="1.526">
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="4.725">
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="6.648">
<failure message="admin-auth.spec.ts:61:6 should show error for invalid credentials" type="FAILURE">
<![CDATA[  [chromium] › admin-auth.spec.ts:61:6 › Admin Authentication › should show error for invalid credentials 

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('text=Invalid username or password')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Invalid username or password')


      68 |
      69 | 		// Wait for error message
    > 70 | 		await expect(page.locator('text=Invalid username or password')).toBeVisible();
         | 		                                                                ^
      71 |
      72 | 		// Should still be on login page
      73 | 		await expect(page).toHaveURL('/admin/login');
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:70:67

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-224fa-ror-for-invalid-credentials-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-224fa-ror-for-invalid-credentials-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentic-224fa-ror-for-invalid-credentials-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-224fa-ror-for-invalid-credentials-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-224fa-ror-for-invalid-credentials-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-224fa-ror-for-invalid-credentials-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="6.65">
<failure message="admin-auth.spec.ts:76:6 should show validation errors for empty fields" type="FAILURE">
<![CDATA[  [chromium] › admin-auth.spec.ts:76:6 › Admin Authentication › should show validation errors for empty fields 

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('text=Please enter both username and password')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Please enter both username and password')


      79 |
      80 | 		// Check for validation messages
    > 81 | 		await expect(page.locator('text=Please enter both username and password')).toBeVisible();
         | 		                                                                           ^
      82 | 	});
      83 |
      84 | 	test('should toggle password visibility', async ({ page }) => {
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:81:78

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="6.429">
<failure message="admin-auth.spec.ts:84:6 should toggle password visibility" type="FAILURE">
<![CDATA[  [chromium] › admin-auth.spec.ts:84:6 › Admin Authentication › should toggle password visibility ──

    Error: Timed out 5000ms waiting for expect(locator).toHaveAttribute(expected)

    Locator: locator('input[id="password"]')
    Expected string: "text"
    Received string: "password"
    Call log:
      - Expect "toHaveAttribute" with timeout 5000ms
      - waiting for locator('input[id="password"]')
        9 × locator resolved to <input value="" required="" id="password" type="password" autocomplete="current-password" placeholder="Enter your password" class="flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 w-full pr-10"/>
          - unexpected value "password"


       98 |
       99 | 		// Password should now be visible
    > 100 | 		await expect(passwordInput).toHaveAttribute('type', 'text');
          | 		                            ^
      101 |
      102 | 		// Click toggle button again
      103 | 		await toggleButton.click();
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:100:31

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-5fc53--toggle-password-visibility-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-5fc53--toggle-password-visibility-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentic-5fc53--toggle-password-visibility-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-5fc53--toggle-password-visibility-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-5fc53--toggle-password-visibility-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-5fc53--toggle-password-visibility-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="1.53">
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="30.331">
<failure message="admin-auth.spec.ts:125:6 should redirect to login when accessing protected route without auth" type="FAILURE">
<![CDATA[  [chromium] › admin-auth.spec.ts:125:6 › Admin Authentication › should redirect to login when accessing protected route without auth 

    Test timeout of 30000ms exceeded.

    Error: page.waitForURL: Test timeout of 30000ms exceeded.
    =========================== logs ===========================
    waiting for navigation to "/admin/login" until "load"
      navigated to "http://localhost:3000/admin"
    ============================================================

      130 |
      131 | 		// Should redirect to login page
    > 132 | 		await page.waitForURL('/admin/login');
          | 		           ^
      133 | 		await expect(page.locator('text=Admin Login')).toBeVisible();
      134 | 	});
      135 |
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:132:14

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-a0986-rotected-route-without-auth-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-a0986-rotected-route-without-auth-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentic-a0986-rotected-route-without-auth-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-a0986-rotected-route-without-auth-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-a0986-rotected-route-without-auth-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-a0986-rotected-route-without-auth-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="1.783">
</testcase>
</testsuite>
<testsuite name="admin-dashboard.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="chromium" tests="12" failures="7" skipped="0" time="65.112" errors="0">
<testcase name="Admin Dashboard › should display dashboard overview correctly" classname="admin-dashboard.spec.ts" time="2.736">
<failure message="admin-dashboard.spec.ts:13:6 should display dashboard overview correctly" type="FAILURE">
<![CDATA[  [chromium] › admin-dashboard.spec.ts:13:6 › Admin Dashboard › should display dashboard overview correctly 

    Error: expect.toBeVisible: Error: strict mode violation: locator('text=Collections') resolved to 3 elements:
        1) <span class="font-medium">Collections</span> aka getByRole('button', { name: 'Collections Manage collections' })
        2) <span class="text-xs opacity-75">Manage collections</span> aka getByRole('button', { name: 'Collections Manage collections' })
        3) <h3 class="tracking-tight text-sm font-medium">Collections</h3> aka getByRole('heading', { name: 'Collections' })

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Collections')


      24 | 		// Check stats cards
      25 | 		await expect(page.locator('text=Total Users')).toBeVisible();
    > 26 | 		await expect(page.locator('text=Collections')).toBeVisible();
         | 		                                               ^
      27 | 		await expect(page.locator('text=Feedback')).toBeVisible();
      28 | 		await expect(page.locator('text=Performance')).toBeVisible();
      29 | 	});
        at /Users/<USER>/Github/vocab/e2e/admin/admin-dashboard.spec.ts:26:50

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-41cad-ashboard-overview-correctly-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-41cad-ashboard-overview-correctly-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-dashboard-Admin-Dash-41cad-ashboard-overview-correctly-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-41cad-ashboard-overview-correctly-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-41cad-ashboard-overview-correctly-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-41cad-ashboard-overview-correctly-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Dashboard › should display system health status" classname="admin-dashboard.spec.ts" time="3.585">
<failure message="admin-dashboard.spec.ts:31:6 should display system health status" type="FAILURE">
<![CDATA[  [chromium] › admin-dashboard.spec.ts:31:6 › Admin Dashboard › should display system health status 

    Error: expect.toBeVisible: Error: strict mode violation: locator('text=Cache') resolved to 2 elements:
        1) <span class="text-gray-600 dark:text-gray-400">Cache:</span> aka getByText('Cache:')
        2) <div class="text-xs text-muted-foreground mt-1">Cache Hit: 85.0%</div> aka getByText('Cache Hit: 85.0%')

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Cache')


      39 | 		// Check health details
      40 | 		await expect(page.locator('text=Database')).toBeVisible();
    > 41 | 		await expect(page.locator('text=Cache')).toBeVisible();
         | 		                                         ^
      42 | 		await expect(page.locator('text=AI Services')).toBeVisible();
      43 | 	});
      44 |
        at /Users/<USER>/Github/vocab/e2e/admin/admin-dashboard.spec.ts:41:44

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-02b98-isplay-system-health-status-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-02b98-isplay-system-health-status-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-dashboard-Admin-Dash-02b98-isplay-system-health-status-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-02b98-isplay-system-health-status-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-02b98-isplay-system-health-status-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-02b98-isplay-system-health-status-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Dashboard › should display user statistics" classname="admin-dashboard.spec.ts" time="7.787">
<failure message="admin-dashboard.spec.ts:45:6 should display user statistics" type="FAILURE">
<![CDATA[  [chromium] › admin-dashboard.spec.ts:45:6 › Admin Dashboard › should display user statistics ─────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('text=Total Users').locator('..').locator('.text-2xl').first()
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Total Users').locator('..').locator('.text-2xl').first()


      53 | 		// Check for numeric values (should be numbers)
      54 | 		const totalUsersValue = page.locator('text=Total Users').locator('..').locator('.text-2xl').first();
    > 55 | 		await expect(totalUsersValue).toBeVisible();
         | 		                              ^
      56 | 		
      57 | 		// Check additional user metrics
      58 | 		await expect(page.locator('text=Active:')).toBeVisible();
        at /Users/<USER>/Github/vocab/e2e/admin/admin-dashboard.spec.ts:55:33

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-5a10e-uld-display-user-statistics-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-5a10e-uld-display-user-statistics-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-dashboard-Admin-Dash-5a10e-uld-display-user-statistics-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-5a10e-uld-display-user-statistics-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-5a10e-uld-display-user-statistics-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-5a10e-uld-display-user-statistics-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Dashboard › should display collection statistics" classname="admin-dashboard.spec.ts" time="1.804">
</testcase>
<testcase name="Admin Dashboard › should display feedback statistics" classname="admin-dashboard.spec.ts" time="2.726">
</testcase>
<testcase name="Admin Dashboard › should display performance metrics" classname="admin-dashboard.spec.ts" time="2.554">
</testcase>
<testcase name="Admin Dashboard › should refresh dashboard data" classname="admin-dashboard.spec.ts" time="2.969">
</testcase>
<testcase name="Admin Dashboard › should navigate to different admin sections" classname="admin-dashboard.spec.ts" time="12.466">
</testcase>
<testcase name="Admin Dashboard › should display sidebar navigation correctly" classname="admin-dashboard.spec.ts" time="2.446">
<failure message="admin-dashboard.spec.ts:148:6 should display sidebar navigation correctly" type="FAILURE">
<![CDATA[  [chromium] › admin-dashboard.spec.ts:148:6 › Admin Dashboard › should display sidebar navigation correctly 

    Error: expect.toBeVisible: Error: strict mode violation: locator('nav').locator('text=Collections') resolved to 2 elements:
        1) <span class="font-medium">Collections</span> aka getByRole('button', { name: 'Collections Manage collections' })
        2) <span class="text-xs opacity-75">Manage collections</span> aka getByRole('button', { name: 'Collections Manage collections' })

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('nav').locator('text=Collections')


      153 | 		await expect(page.locator('nav').locator('text=Dashboard')).toBeVisible();
      154 | 		await expect(page.locator('nav').locator('text=Users')).toBeVisible();
    > 155 | 		await expect(page.locator('nav').locator('text=Collections')).toBeVisible();
          | 		                                                              ^
      156 | 		await expect(page.locator('nav').locator('text=Feedback')).toBeVisible();
      157 | 		await expect(page.locator('nav').locator('text=System')).toBeVisible();
      158 | 		await expect(page.locator('nav').locator('text=Audit Logs')).toBeVisible();
        at /Users/<USER>/Github/vocab/e2e/admin/admin-dashboard.spec.ts:155:65

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-949b6-idebar-navigation-correctly-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-949b6-idebar-navigation-correctly-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-dashboard-Admin-Dash-949b6-idebar-navigation-correctly-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-949b6-idebar-navigation-correctly-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-949b6-idebar-navigation-correctly-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-949b6-idebar-navigation-correctly-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Dashboard › should handle mobile navigation" classname="admin-dashboard.spec.ts" time="12.186">
<failure message="admin-dashboard.spec.ts:164:6 should handle mobile navigation" type="FAILURE">
<![CDATA[  [chromium] › admin-dashboard.spec.ts:164:6 › Admin Dashboard › should handle mobile navigation ───

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('button').filter({ has: locator('svg') }).first()
        - locator resolved to <button id="_r_2_" tabindex="0" role="button" data-slot="button" aria-disabled="false" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:bord…>…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is not stable
        - retrying click action
        - waiting 20ms
        - waiting for element to be visible, enabled and stable
        - element is not stable
      2 × retrying click action
          - waiting 100ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      17 × retrying click action
           - waiting 500ms
           - waiting for element to be visible, enabled and stable
           - element is visible, enabled and stable
           - scrolling into view if needed
           - done scrolling
           - element is outside of the viewport
      - retrying click action
        - waiting 500ms


      172 | 		// Click mobile menu button if it exists
      173 | 		if (await mobileMenuButton.isVisible()) {
    > 174 | 			await mobileMenuButton.click();
          | 			                       ^
      175 | 			
      176 | 			// Check if navigation becomes visible
      177 | 			await expect(page.locator('nav')).toBeVisible();
        at /Users/<USER>/Github/vocab/e2e/admin/admin-dashboard.spec.ts:174:27

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-7767d-ld-handle-mobile-navigation-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-7767d-ld-handle-mobile-navigation-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-dashboard-Admin-Dash-7767d-ld-handle-mobile-navigation-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-7767d-ld-handle-mobile-navigation-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-7767d-ld-handle-mobile-navigation-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-7767d-ld-handle-mobile-navigation-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Dashboard › should display error state gracefully" classname="admin-dashboard.spec.ts" time="6.967">
<failure message="admin-dashboard.spec.ts:181:6 should display error state gracefully" type="FAILURE">
<![CDATA[  [chromium] › admin-dashboard.spec.ts:181:6 › Admin Dashboard › should display error state gracefully 

    Error: Timed out 5000ms waiting for expect(locator).not.toBeVisible()

    Locator: locator('text=Error')
    Expected: not visible
    Received: visible
    Call log:
      - Expect "not toBeVisible" with timeout 5000ms
      - waiting for locator('text=Error')
        9 × locator resolved to <div class="text-xs text-muted-foreground">Error Rate: 2.00%</div>
          - unexpected value "visible"


      186 | 		// Check that no error messages are displayed on successful load
      187 | 		await expect(page.locator('text=Failed to load')).not.toBeVisible();
    > 188 | 		await expect(page.locator('text=Error')).not.toBeVisible();
          | 		                                             ^
      189 | 	});
      190 |
      191 | 	test('should maintain active navigation state', async ({ page }) => {
        at /Users/<USER>/Github/vocab/e2e/admin/admin-dashboard.spec.ts:188:48

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-2c393-play-error-state-gracefully-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-2c393-play-error-state-gracefully-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-dashboard-Admin-Dash-2c393-play-error-state-gracefully-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-2c393-play-error-state-gracefully-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-2c393-play-error-state-gracefully-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-2c393-play-error-state-gracefully-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Dashboard › should maintain active navigation state" classname="admin-dashboard.spec.ts" time="6.886">
<failure message="admin-dashboard.spec.ts:191:6 should maintain active navigation state" type="FAILURE">
<![CDATA[  [chromium] › admin-dashboard.spec.ts:191:6 › Admin Dashboard › should maintain active navigation state 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('nav').locator('text=Dashboard').locator('..')
    Expected pattern: /bg-blue-600|default/
    Received string:  "flex flex-col items-start"
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('nav').locator('text=Dashboard').locator('..')
        9 × locator resolved to <div class="flex flex-col items-start">…</div>
          - unexpected value "flex flex-col items-start"


      192 | 		// Dashboard should be active initially
      193 | 		const dashboardNav = page.locator('nav').locator('text=Dashboard').locator('..');
    > 194 | 		await expect(dashboardNav).toHaveClass(/bg-blue-600|default/);
          | 		                           ^
      195 | 		
      196 | 		// Navigate to Users
      197 | 		await page.click('text=Users');
        at /Users/<USER>/Github/vocab/e2e/admin/admin-dashboard.spec.ts:194:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-2aa4a-ain-active-navigation-state-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-2aa4a-ain-active-navigation-state-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-dashboard-Admin-Dash-2aa4a-ain-active-navigation-state-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-2aa4a-ain-active-navigation-state-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-2aa4a-ain-active-navigation-state-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-2aa4a-ain-active-navigation-state-chromium/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="admin-integration.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="chromium" tests="10" failures="8" skipped="0" time="105.235" errors="0">
<testcase name="Admin Integration Tests › should complete full admin workflow" classname="admin-integration.spec.ts" time="4.703">
<failure message="admin-integration.spec.ts:13:6 should complete full admin workflow" type="FAILURE">
<![CDATA[  [chromium] › admin-integration.spec.ts:13:6 › Admin Integration Tests › should complete full admin workflow 

    TypeError: page.click(...).filter is not a function

      22 | 		// 3. Search for a user
      23 | 		await page.fill('input[placeholder="Search users..."]', 'admin');
    > 24 | 		await page.click('button').filter({ hasText: 'Search' });
         | 		                           ^
      25 | 		await page.waitForTimeout(1000);
      26 | 		
      27 | 		// 4. Navigate to collections
        at /Users/<USER>/Github/vocab/e2e/admin/admin-integration.spec.ts:24:30

    Error: page.click: Test ended.
    Call log:
      - waiting for locator('button')
        - locator resolved to 31 elements. Proceeding with the first one: <button id="_r_d_" tabindex="0" role="button" data-slot="button" aria-disabled="false" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:bord…>…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is not visible
        - retrying click action
        - waiting 20ms
        - waiting for element to be visible, enabled and stable
        - element is not visible
      - retrying click action
        - waiting 100ms


      22 | 		// 3. Search for a user
      23 | 		await page.fill('input[placeholder="Search users..."]', 'admin');
    > 24 | 		await page.click('button').filter({ hasText: 'Search' });
         | 		           ^
      25 | 		await page.waitForTimeout(1000);
      26 | 		
      27 | 		// 4. Navigate to collections
        at /Users/<USER>/Github/vocab/e2e/admin/admin-integration.spec.ts:24:14

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-31bb0-omplete-full-admin-workflow-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-integration-Admin-In-31bb0-omplete-full-admin-workflow-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-31bb0-omplete-full-admin-workflow-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-31bb0-omplete-full-admin-workflow-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Integration Tests › should maintain session across navigation" classname="admin-integration.spec.ts" time="30.29">
<failure message="admin-integration.spec.ts:62:6 should maintain session across navigation" type="FAILURE">
<![CDATA[  [chromium] › admin-integration.spec.ts:62:6 › Admin Integration Tests › should maintain session across navigation 

    Test timeout of 30000ms exceeded.

    Error: page.waitForURL: Test timeout of 30000ms exceeded.
    =========================== logs ===========================
    waiting for navigation to "/admin/feedback" until "load"
    ============================================================

      72 | 		for (const adminPage of pages) {
      73 | 			await page.click(`text=${adminPage.link}`);
    > 74 | 			await page.waitForURL(adminPage.url);
         | 			           ^
      75 | 			
      76 | 			// Should not redirect to login (session maintained)
      77 | 			await expect(page).toHaveURL(adminPage.url);
        at /Users/<USER>/Github/vocab/e2e/admin/admin-integration.spec.ts:74:15

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-ab9c4-n-session-across-navigation-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-ab9c4-n-session-across-navigation-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-integration-Admin-In-ab9c4-n-session-across-navigation-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-ab9c4-n-session-across-navigation-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-ab9c4-n-session-across-navigation-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-ab9c4-n-session-across-navigation-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Integration Tests › should handle concurrent admin operations" classname="admin-integration.spec.ts" time="9.102">
<failure message="admin-integration.spec.ts:84:6 should handle concurrent admin operations" type="FAILURE">
<![CDATA[  [chromium] › admin-integration.spec.ts:84:6 › Admin Integration Tests › should handle concurrent admin operations 

    Error: expect.toBeVisible: Error: strict mode violation: locator('text=System Health') resolved to 2 elements:
        1) <p class="text-gray-600 dark:text-gray-400 mt-1">Monitor system health and performance</p> aka getByText('Monitor system health and')
        2) <h3 class="font-semibold leading-none tracking-tight flex items-center">…</h3> aka getByRole('heading', { name: 'System Health' })

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=System Health')


      102 | 		// Page should still be functional
      103 | 		await expect(page.locator('h1')).toContainText('System Monitoring');
    > 104 | 		await expect(page.locator('text=System Health')).toBeVisible();
          | 		                                                 ^
      105 | 	});
      106 |
      107 | 	test('should handle admin logout and re-login', async ({ page }) => {
        at /Users/<USER>/Github/vocab/e2e/admin/admin-integration.spec.ts:104:52

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-87c8a-concurrent-admin-operations-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-87c8a-concurrent-admin-operations-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-integration-Admin-In-87c8a-concurrent-admin-operations-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-87c8a-concurrent-admin-operations-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-87c8a-concurrent-admin-operations-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-87c8a-concurrent-admin-operations-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Integration Tests › should handle admin logout and re-login" classname="admin-integration.spec.ts" time="10.846">
<failure message="admin-integration.spec.ts:107:6 should handle admin logout and re-login" type="FAILURE">
<![CDATA[  [chromium] › admin-integration.spec.ts:107:6 › Admin Integration Tests › should handle admin logout and re-login 

    Error: Timed out 5000ms waiting for expect(locator).toContainText(expected)

    Locator: locator('h1')
    Expected string: "Admin Login"
    Received: <element(s) not found>
    Call log:
      - Expect "toContainText" with timeout 5000ms
      - waiting for locator('h1')


      115 | 		// Should redirect to login
      116 | 		await page.waitForURL('/admin/login');
    > 117 | 		await expect(page.locator('h1')).toContainText('Admin Login');
          | 		                                 ^
      118 | 		
      119 | 		// Try to access protected route
      120 | 		await page.goto('/admin/users');
        at /Users/<USER>/Github/vocab/e2e/admin/admin-integration.spec.ts:117:36

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-28e6f-e-admin-logout-and-re-login-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-28e6f-e-admin-logout-and-re-login-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-integration-Admin-In-28e6f-e-admin-logout-and-re-login-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-28e6f-e-admin-logout-and-re-login-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-28e6f-e-admin-logout-and-re-login-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-28e6f-e-admin-logout-and-re-login-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Integration Tests › should handle browser refresh on admin pages" classname="admin-integration.spec.ts" time="7.097">
</testcase>
<testcase name="Admin Integration Tests › should handle admin operations with error recovery" classname="admin-integration.spec.ts" time="2.956">
<failure message="admin-integration.spec.ts:149:6 should handle admin operations with error recovery" type="FAILURE">
<![CDATA[  [chromium] › admin-integration.spec.ts:149:6 › Admin Integration Tests › should handle admin operations with error recovery 

    TypeError: page.click(...).filter is not a function

      154 | 		// Try to search for non-existent user
      155 | 		await page.fill('input[placeholder="Search users..."]', 'nonexistentuser12345');
    > 156 | 		await page.click('button').filter({ hasText: 'Search' });
          | 		                           ^
      157 | 		await page.waitForTimeout(1000);
      158 | 		
      159 | 		// Clear search and try again
        at /Users/<USER>/Github/vocab/e2e/admin/admin-integration.spec.ts:156:30

    Error: page.click: Test ended.
    Call log:
      - waiting for locator('button')
        - locator resolved to 31 elements. Proceeding with the first one: <button id="_r_d_" tabindex="0" role="button" data-slot="button" aria-disabled="false" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:bord…>…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is not visible
        - retrying click action
        - waiting 20ms
        - waiting for element to be visible, enabled and stable
        - element is not visible
      - retrying click action
        - waiting 100ms


      154 | 		// Try to search for non-existent user
      155 | 		await page.fill('input[placeholder="Search users..."]', 'nonexistentuser12345');
    > 156 | 		await page.click('button').filter({ hasText: 'Search' });
          | 		           ^
      157 | 		await page.waitForTimeout(1000);
      158 | 		
      159 | 		// Clear search and try again
        at /Users/<USER>/Github/vocab/e2e/admin/admin-integration.spec.ts:156:14

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-4f5f8-rations-with-error-recovery-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-integration-Admin-In-4f5f8-rations-with-error-recovery-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-4f5f8-rations-with-error-recovery-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-4f5f8-rations-with-error-recovery-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Integration Tests › should maintain admin privileges throughout session" classname="admin-integration.spec.ts" time="7.561">
</testcase>
<testcase name="Admin Integration Tests › should handle mobile responsive admin interface" classname="admin-integration.spec.ts" time="14.069">
<failure message="admin-integration.spec.ts:195:6 should handle mobile responsive admin interface" type="FAILURE">
<![CDATA[  [chromium] › admin-integration.spec.ts:195:6 › Admin Integration Tests › should handle mobile responsive admin interface 

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('button').filter({ has: locator('svg') }).first()
        - locator resolved to <button id="_r_2_" tabindex="0" role="button" data-slot="button" aria-disabled="false" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:bord…>…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is not stable
        - retrying click action
        - waiting 20ms
        2 × waiting for element to be visible, enabled and stable
          - element is not stable
        - retrying click action
          - waiting 100ms
        18 × waiting for element to be visible, enabled and stable
           - element is visible, enabled and stable
           - scrolling into view if needed
           - done scrolling
           - element is outside of the viewport
         - retrying click action
           - waiting 500ms


      204 | 			const mobileMenuButton = page.locator('button').filter({ has: page.locator('svg') }).first();
      205 | 			if (await mobileMenuButton.isVisible()) {
    > 206 | 				await mobileMenuButton.click();
          | 				                       ^
      207 | 			}
      208 | 			
      209 | 			// Click section
        at /Users/<USER>/Github/vocab/e2e/admin/admin-integration.spec.ts:206:28

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-3f3dd--responsive-admin-interface-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-3f3dd--responsive-admin-interface-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-integration-Admin-In-3f3dd--responsive-admin-interface-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-3f3dd--responsive-admin-interface-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-3f3dd--responsive-admin-interface-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-3f3dd--responsive-admin-interface-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Integration Tests › should handle admin data consistency" classname="admin-integration.spec.ts" time="13.033">
<failure message="admin-integration.spec.ts:224:6 should handle admin data consistency" type="FAILURE">
<![CDATA[  [chromium] › admin-integration.spec.ts:224:6 › Admin Integration Tests › should handle admin data consistency 

    TimeoutError: locator.textContent: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('text=Total Users').locator('..').locator('[class*="text-2xl"]')


      225 | 		// Get initial user count from dashboard
      226 | 		await expect(page.locator('text=Total Users')).toBeVisible();
    > 227 | 		const dashboardUserCount = await page.locator('text=Total Users').locator('..').locator('[class*="text-2xl"]').textContent();
          | 		                                                                                                               ^
      228 | 		
      229 | 		// Navigate to user management
      230 | 		await page.click('text=Users');
        at /Users/<USER>/Github/vocab/e2e/admin/admin-integration.spec.ts:227:114

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-2ab62-ndle-admin-data-consistency-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-2ab62-ndle-admin-data-consistency-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-integration-Admin-In-2ab62-ndle-admin-data-consistency-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-2ab62-ndle-admin-data-consistency-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-2ab62-ndle-admin-data-consistency-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-2ab62-ndle-admin-data-consistency-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Integration Tests › should handle admin error states gracefully" classname="admin-integration.spec.ts" time="5.578">
<failure message="admin-integration.spec.ts:254:6 should handle admin error states gracefully" type="FAILURE">
<![CDATA[  [chromium] › admin-integration.spec.ts:254:6 › Admin Integration Tests › should handle admin error states gracefully 

    Error: expect.toBeVisible: Error: strict mode violation: locator('text=System Health') resolved to 2 elements:
        1) <p class="text-gray-600 dark:text-gray-400 mt-1">Monitor system health and performance</p> aka getByText('Monitor system health and')
        2) <h3 class="font-semibold leading-none tracking-tight flex items-center">…</h3> aka getByRole('heading', { name: 'System Health' })

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=System Health')


      279 | 		} else {
      280 | 			// If no error, should show normal content
    > 281 | 			await expect(page.locator('text=System Health')).toBeVisible();
          | 			                                                 ^
      282 | 		}
      283 | 	});
      284 | });
        at /Users/<USER>/Github/vocab/e2e/admin/admin-integration.spec.ts:281:53

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-2f84e-min-error-states-gracefully-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-2f84e-min-error-states-gracefully-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-integration-Admin-In-2f84e-min-error-states-gracefully-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-2f84e-min-error-states-gracefully-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-2f84e-min-error-states-gracefully-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-2f84e-min-error-states-gracefully-chromium/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="audit-logs.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="chromium" tests="18" failures="11" skipped="0" time="152.106" errors="0">
<testcase name="Audit Logs › should display audit logs page correctly" classname="audit-logs.spec.ts" time="7.103">
<failure message="audit-logs.spec.ts:17:6 should display audit logs page correctly" type="FAILURE">
<![CDATA[  [chromium] › audit-logs.spec.ts:17:6 › Audit Logs › should display audit logs page correctly ─────

    Error: Timed out 5000ms waiting for expect(locator).toContainText(expected)

    Locator: locator('h1')
    Expected string: "Audit Logs"
    Received string: "Something went wrong"
    Call log:
      - Expect "toContainText" with timeout 5000ms
      - waiting for locator('h1')
        7 × locator resolved to <h1 class="text-xl font-semibold text-gray-900 dark:text-white text-center mb-2">Something went wrong</h1>
          - unexpected value "Something went wrong"


      17 | 	test('should display audit logs page correctly', async ({ page }) => {
      18 | 		// Check page title and description
    > 19 | 		await expect(page.locator('h1')).toContainText('Audit Logs');
         | 		                                 ^
      20 | 		await expect(page.locator('text=System activity and security audit trail')).toBeVisible();
      21 | 		
      22 | 		// Check refresh button
        at /Users/<USER>/Github/vocab/e2e/admin/audit-logs.spec.ts:19:36

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/audit-logs-Audit-Logs-shou-158dd-y-audit-logs-page-correctly-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/audit-logs-Audit-Logs-shou-158dd-y-audit-logs-page-correctly-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/audit-logs-Audit-Logs-shou-158dd-y-audit-logs-page-correctly-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-shou-158dd-y-audit-logs-page-correctly-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-shou-158dd-y-audit-logs-page-correctly-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-shou-158dd-y-audit-logs-page-correctly-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Audit Logs › should display filters section" classname="audit-logs.spec.ts" time="7.444">
<failure message="audit-logs.spec.ts:26:6 should display filters section" type="FAILURE">
<![CDATA[  [chromium] › audit-logs.spec.ts:26:6 › Audit Logs › should display filters section ───────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('text=Filters')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Filters')


      26 | 	test('should display filters section', async ({ page }) => {
      27 | 		// Check filters section
    > 28 | 		await expect(page.locator('text=Filters')).toBeVisible();
         | 		                                           ^
      29 | 		
      30 | 		// Check filter inputs
      31 | 		await expect(page.locator('input[placeholder*="action"]')).toBeVisible();
        at /Users/<USER>/Github/vocab/e2e/admin/audit-logs.spec.ts:28:46

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/audit-logs-Audit-Logs-should-display-filters-section-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/audit-logs-Audit-Logs-should-display-filters-section-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/audit-logs-Audit-Logs-should-display-filters-section-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-display-filters-section-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-display-filters-section-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-display-filters-section-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Audit Logs › should display audit events section" classname="audit-logs.spec.ts" time="9.975">
<failure message="audit-logs.spec.ts:43:6 should display audit events section" type="FAILURE">
<![CDATA[  [chromium] › audit-logs.spec.ts:43:6 › Audit Logs › should display audit events section ──────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('text=Audit Events')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Audit Events')


      46 | 		
      47 | 		// Check audit events section
    > 48 | 		await expect(page.locator('text=Audit Events')).toBeVisible();
         | 		                                                ^
      49 | 		
      50 | 		// Either show events or empty state
      51 | 		const hasEvents = await page.locator('[class*="border"]').filter({ has: page.locator('text=admin_login') }).isVisible();
        at /Users/<USER>/Github/vocab/e2e/admin/audit-logs.spec.ts:48:51

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/audit-logs-Audit-Logs-should-display-audit-events-section-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/audit-logs-Audit-Logs-should-display-audit-events-section-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/audit-logs-Audit-Logs-should-display-audit-events-section-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-display-audit-events-section-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-display-audit-events-section-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-display-audit-events-section-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Audit Logs › should display audit event details" classname="audit-logs.spec.ts" time="3.95">
</testcase>
<testcase name="Audit Logs › should filter audit logs by action" classname="audit-logs.spec.ts" time="14.631">
<failure message="audit-logs.spec.ts:82:6 should filter audit logs by action" type="FAILURE">
<![CDATA[  [chromium] › audit-logs.spec.ts:82:6 › Audit Logs › should filter audit logs by action ───────────

    TimeoutError: locator.fill: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('input[placeholder*="action"]')


      86 | 		// Fill action filter
      87 | 		const actionInput = page.locator('input[placeholder*="action"]');
    > 88 | 		await actionInput.fill('admin_login');
         | 		                  ^
      89 | 		
      90 | 		// Click filter button
      91 | 		await page.click('button').filter({ hasText: 'Filter' });
        at /Users/<USER>/Github/vocab/e2e/admin/audit-logs.spec.ts:88:21

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/audit-logs-Audit-Logs-should-filter-audit-logs-by-action-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/audit-logs-Audit-Logs-should-filter-audit-logs-by-action-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/audit-logs-Audit-Logs-should-filter-audit-logs-by-action-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-filter-audit-logs-by-action-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-filter-audit-logs-by-action-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-filter-audit-logs-by-action-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Audit Logs › should filter audit logs by resource" classname="audit-logs.spec.ts" time="14.471">
<failure message="audit-logs.spec.ts:103:6 should filter audit logs by resource" type="FAILURE">
<![CDATA[  [chromium] › audit-logs.spec.ts:103:6 › Audit Logs › should filter audit logs by resource ────────

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('select, [role="combobox"]').first()


      107 | 		// Open resource filter dropdown
      108 | 		const resourceSelect = page.locator('select, [role="combobox"]').first();
    > 109 | 		await resourceSelect.click();
          | 		                     ^
      110 | 		
      111 | 		// Select a resource type
      112 | 		const userOption = page.locator('text=User').or(page.locator('option[value="user"]'));
        at /Users/<USER>/Github/vocab/e2e/admin/audit-logs.spec.ts:109:24

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/audit-logs-Audit-Logs-should-filter-audit-logs-by-resource-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/audit-logs-Audit-Logs-should-filter-audit-logs-by-resource-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/audit-logs-Audit-Logs-should-filter-audit-logs-by-resource-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-filter-audit-logs-by-resource-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-filter-audit-logs-by-resource-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-filter-audit-logs-by-resource-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Audit Logs › should filter audit logs by user ID" classname="audit-logs.spec.ts" time="13.69">
<failure message="audit-logs.spec.ts:127:6 should filter audit logs by user ID" type="FAILURE">
<![CDATA[  [chromium] › audit-logs.spec.ts:127:6 › Audit Logs › should filter audit logs by user ID ─────────

    TimeoutError: locator.fill: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('input[placeholder*="user"]')


      131 | 		// Fill user ID filter
      132 | 		const userIdInput = page.locator('input[placeholder*="user"]');
    > 133 | 		await userIdInput.fill('test-user-id');
          | 		                  ^
      134 | 		
      135 | 		// Click filter button
      136 | 		await page.click('button').filter({ hasText: 'Filter' });
        at /Users/<USER>/Github/vocab/e2e/admin/audit-logs.spec.ts:133:21

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/audit-logs-Audit-Logs-should-filter-audit-logs-by-user-ID-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/audit-logs-Audit-Logs-should-filter-audit-logs-by-user-ID-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/audit-logs-Audit-Logs-should-filter-audit-logs-by-user-ID-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-filter-audit-logs-by-user-ID-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-filter-audit-logs-by-user-ID-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-filter-audit-logs-by-user-ID-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Audit Logs › should clear filters" classname="audit-logs.spec.ts" time="13.68">
<failure message="audit-logs.spec.ts:148:6 should clear filters" type="FAILURE">
<![CDATA[  [chromium] › audit-logs.spec.ts:148:6 › Audit Logs › should clear filters ────────────────────────

    TimeoutError: page.fill: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('input[placeholder*="action"]')


      151 | 		
      152 | 		// Fill some filters
    > 153 | 		await page.fill('input[placeholder*="action"]', 'test_action');
          | 		           ^
      154 | 		await page.fill('input[placeholder*="user"]', 'test_user');
      155 | 		
      156 | 		// Click clear button
        at /Users/<USER>/Github/vocab/e2e/admin/audit-logs.spec.ts:153:14

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/audit-logs-Audit-Logs-should-clear-filters-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/audit-logs-Audit-Logs-should-clear-filters-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/audit-logs-Audit-Logs-should-clear-filters-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-clear-filters-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-clear-filters-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-clear-filters-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Audit Logs › should display event badges with correct colors" classname="audit-logs.spec.ts" time="4.47">
</testcase>
<testcase name="Audit Logs › should display event icons correctly" classname="audit-logs.spec.ts" time="4.338">
</testcase>
<testcase name="Audit Logs › should display event metadata" classname="audit-logs.spec.ts" time="3.692">
</testcase>
<testcase name="Audit Logs › should handle pagination if available" classname="audit-logs.spec.ts" time="5.414">
</testcase>
<testcase name="Audit Logs › should display pagination information" classname="audit-logs.spec.ts" time="4.756">
</testcase>
<testcase name="Audit Logs › should refresh audit logs" classname="audit-logs.spec.ts" time="11.83">
<failure message="audit-logs.spec.ts:257:6 should refresh audit logs" type="FAILURE">
<![CDATA[  [chromium] › audit-logs.spec.ts:257:6 › Audit Logs › should refresh audit logs ───────────────────

    Error: Timed out 5000ms waiting for expect(locator).toContainText(expected)

    Locator: locator('h1')
    Expected string: "Audit Logs"
    Received string: "Something went wrong"
    Call log:
      - Expect "toContainText" with timeout 5000ms
      - waiting for locator('h1')
        8 × locator resolved to <h1 class="text-xl font-semibold text-gray-900 dark:text-white text-center mb-2">Something went wrong</h1>
          - unexpected value "Something went wrong"


      267 | 		
      268 | 		// Verify page is still functional
    > 269 | 		await expect(page.locator('h1')).toContainText('Audit Logs');
          | 		                                 ^
      270 | 		await expect(page.locator('text=Filters')).toBeVisible();
      271 | 	});
      272 |
        at /Users/<USER>/Github/vocab/e2e/admin/audit-logs.spec.ts:269:36

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/audit-logs-Audit-Logs-should-refresh-audit-logs-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/audit-logs-Audit-Logs-should-refresh-audit-logs-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/audit-logs-Audit-Logs-should-refresh-audit-logs-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-refresh-audit-logs-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-refresh-audit-logs-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-refresh-audit-logs-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Audit Logs › should handle empty audit logs state" classname="audit-logs.spec.ts" time="10.199">
<failure message="audit-logs.spec.ts:273:6 should handle empty audit logs state" type="FAILURE">
<![CDATA[  [chromium] › audit-logs.spec.ts:273:6 › Audit Logs › should handle empty audit logs state ────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('text=No audit logs found')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=No audit logs found')


      280 | 		
      281 | 		if (!hasEvents) {
    > 282 | 			await expect(emptyState).toBeVisible();
          | 			                         ^
      283 | 			
      284 | 			// Should show file icon in empty state
      285 | 			const fileIcon = page.locator('svg').filter({ has: page.locator('path') });
        at /Users/<USER>/Github/vocab/e2e/admin/audit-logs.spec.ts:282:29

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/audit-logs-Audit-Logs-should-handle-empty-audit-logs-state-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/audit-logs-Audit-Logs-should-handle-empty-audit-logs-state-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/audit-logs-Audit-Logs-should-handle-empty-audit-logs-state-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-handle-empty-audit-logs-state-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-handle-empty-audit-logs-state-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-handle-empty-audit-logs-state-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Audit Logs › should display JSON event details" classname="audit-logs.spec.ts" time="4.326">
</testcase>
<testcase name="Audit Logs › should handle loading states" classname="audit-logs.spec.ts" time="10.383">
<failure message="audit-logs.spec.ts:308:6 should handle loading states" type="FAILURE">
<![CDATA[  [chromium] › audit-logs.spec.ts:308:6 › Audit Logs › should handle loading states ────────────────

    Error: Timed out 5000ms waiting for expect(locator).toContainText(expected)

    Locator: locator('h1')
    Expected string: "Audit Logs"
    Received string: "Something went wrong"
    Call log:
      - Expect "toContainText" with timeout 5000ms
      - waiting for locator('h1')
        9 × locator resolved to <h1 class="text-xl font-semibold text-gray-900 dark:text-white text-center mb-2">Something went wrong</h1>
          - unexpected value "Something went wrong"


      318 | 		
      319 | 		// Page content should be visible
    > 320 | 		await expect(page.locator('h1')).toContainText('Audit Logs');
          | 		                                 ^
      321 | 		await expect(page.locator('text=Filters')).toBeVisible();
      322 | 	});
      323 |
        at /Users/<USER>/Github/vocab/e2e/admin/audit-logs.spec.ts:320:36

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/audit-logs-Audit-Logs-should-handle-loading-states-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/audit-logs-Audit-Logs-should-handle-loading-states-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/audit-logs-Audit-Logs-should-handle-loading-states-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-handle-loading-states-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-handle-loading-states-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-handle-loading-states-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Audit Logs › should maintain responsive layout" classname="audit-logs.spec.ts" time="7.754">
<failure message="audit-logs.spec.ts:324:6 should maintain responsive layout" type="FAILURE">
<![CDATA[  [chromium] › audit-logs.spec.ts:324:6 › Audit Logs › should maintain responsive layout ───────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('text=Filters')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Filters')


      328 | 		
      329 | 		// Should show filters in grid layout
    > 330 | 		await expect(page.locator('text=Filters')).toBeVisible();
          | 		                                           ^
      331 | 		
      332 | 		// Test mobile layout
      333 | 		await page.setViewportSize({ width: 375, height: 667 });
        at /Users/<USER>/Github/vocab/e2e/admin/audit-logs.spec.ts:330:46

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/audit-logs-Audit-Logs-should-maintain-responsive-layout-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/audit-logs-Audit-Logs-should-maintain-responsive-layout-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/audit-logs-Audit-Logs-should-maintain-responsive-layout-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-maintain-responsive-layout-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-maintain-responsive-layout-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-maintain-responsive-layout-chromium/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="collection-management.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="chromium" tests="15" failures="3" skipped="0" time="71.324" errors="0">
<testcase name="Collection Management › should display collection management page correctly" classname="collection-management.spec.ts" time="2.36">
</testcase>
<testcase name="Collection Management › should display collections in grid layout" classname="collection-management.spec.ts" time="3.798">
</testcase>
<testcase name="Collection Management › should display collection cards with correct information" classname="collection-management.spec.ts" time="3.589">
<failure message="collection-management.spec.ts:40:6 should display collection cards with correct information" type="FAILURE">
<![CDATA[  [chromium] › collection-management.spec.ts:40:6 › Collection Management › should display collection cards with correct information 

    Error: expect.toBeVisible: Error: strict mode violation: locator('[class*="card"]').filter({ has: locator('text=Words') }).first().locator('text=EN').or(locator('[class*="card"]').filter({ has: locator('text=Words') }).first().locator('text=VI')) resolved to 2 elements:
        1) <span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent [a&]:hover:bg-primary/90 bg-green-100 text-green-800 da…>VI</span> aka getByText('VI').nth(1)
        2) <span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent [a&]:hover:bg-primary/90 bg-blue-100 text-blue-800 dark…>EN</span> aka getByText('EN').nth(3)

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[class*="card"]').filter({ has: locator('text=Words') }).first().locator('text=EN').or(locator('[class*="card"]').filter({ has: locator('text=Words') }).first().locator('text=VI'))


      53 | 			
      54 | 			// Check for language badges
    > 55 | 			await expect(firstCard.locator('text=EN').or(firstCard.locator('text=VI'))).toBeVisible();
         | 			                                                                            ^
      56 | 			
      57 | 			// Check for content statistics
      58 | 			await expect(firstCard.locator('text=Words')).toBeVisible();
        at /Users/<USER>/Github/vocab/e2e/admin/collection-management.spec.ts:55:80

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/collection-management-Coll-2ec7f-ds-with-correct-information-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/collection-management-Coll-2ec7f-ds-with-correct-information-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/collection-management-Coll-2ec7f-ds-with-correct-information-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/collection-management-Coll-2ec7f-ds-with-correct-information-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/collection-management-Coll-2ec7f-ds-with-correct-information-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/collection-management-Coll-2ec7f-ds-with-correct-information-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Collection Management › should display language badges correctly" classname="collection-management.spec.ts" time="5.546">
</testcase>
<testcase name="Collection Management › should display content statistics" classname="collection-management.spec.ts" time="5.771">
<failure message="collection-management.spec.ts:89:6 should display content statistics" type="FAILURE">
<![CDATA[  [chromium] › collection-management.spec.ts:89:6 › Collection Management › should display content statistics 

    Error: expect.toBeVisible: Error: strict mode violation: locator('[class*="card"]').filter({ has: locator('text=Words') }).first().locator('text=Words').locator('..').locator('[class*="font-semibold"]') resolved to 2 elements:
        1) <div class="font-semibold text-blue-600">0</div> aka getByText('0', { exact: true }).first()
        2) <div class="font-semibold text-purple-600">0</div> aka getByText('0', { exact: true }).nth(2)

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[class*="card"]').filter({ has: locator('text=Words') }).first().locator('text=Words').locator('..').locator('[class*="font-semibold"]')


      102 | 			const keywordsCount = firstCard.locator('text=Keywords').locator('..').locator('[class*="font-semibold"]');
      103 | 			
    > 104 | 			await expect(wordsCount).toBeVisible();
          | 			                         ^
      105 | 			await expect(paragraphsCount).toBeVisible();
      106 | 			await expect(keywordsCount).toBeVisible();
      107 | 		}
        at /Users/<USER>/Github/vocab/e2e/admin/collection-management.spec.ts:104:29

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/collection-management-Coll-e8df7--display-content-statistics-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/collection-management-Coll-e8df7--display-content-statistics-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/collection-management-Coll-e8df7--display-content-statistics-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/collection-management-Coll-e8df7--display-content-statistics-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/collection-management-Coll-e8df7--display-content-statistics-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/collection-management-Coll-e8df7--display-content-statistics-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Collection Management › should display collection metadata" classname="collection-management.spec.ts" time="3.955">
</testcase>
<testcase name="Collection Management › should handle collection deletion with confirmation" classname="collection-management.spec.ts" time="5.237">
</testcase>
<testcase name="Collection Management › should handle view collection action" classname="collection-management.spec.ts" time="5.152">
</testcase>
<testcase name="Collection Management › should refresh collections data" classname="collection-management.spec.ts" time="5.776">
</testcase>
<testcase name="Collection Management › should handle pagination if available" classname="collection-management.spec.ts" time="5.323">
</testcase>
<testcase name="Collection Management › should display pagination information" classname="collection-management.spec.ts" time="5.338">
</testcase>
<testcase name="Collection Management › should handle empty state correctly" classname="collection-management.spec.ts" time="5.457">
</testcase>
<testcase name="Collection Management › should display collection icons correctly" classname="collection-management.spec.ts" time="3.804">
</testcase>
<testcase name="Collection Management › should handle loading states" classname="collection-management.spec.ts" time="5.63">
</testcase>
<testcase name="Collection Management › should maintain responsive layout" classname="collection-management.spec.ts" time="4.588">
<failure message="collection-management.spec.ts:280:6 should maintain responsive layout" type="FAILURE">
<![CDATA[  [chromium] › collection-management.spec.ts:280:6 › Collection Management › should maintain responsive layout 

    Error: expect.toBeVisible: Error: strict mode violation: locator('[class*="grid"]') resolved to 3 elements:
        1) <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">…</div> aka locator('.grid').first()
        2) <div class="grid grid-cols-3 gap-2 text-sm">…</div> aka getByText('0Words0Paragraphs0Keywords')
        3) <div class="grid grid-cols-3 gap-2 text-sm">…</div> aka getByText('9Words0Paragraphs0Keywords')

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[class*="grid"]')


      285 | 		// Should show grid layout
      286 | 		const grid = page.locator('[class*="grid"]');
    > 287 | 		await expect(grid).toBeVisible();
          | 		                   ^
      288 | 		
      289 | 		// Test mobile layout
      290 | 		await page.setViewportSize({ width: 375, height: 667 });
        at /Users/<USER>/Github/vocab/e2e/admin/collection-management.spec.ts:287:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/collection-management-Coll-31097--maintain-responsive-layout-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/collection-management-Coll-31097--maintain-responsive-layout-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/collection-management-Coll-31097--maintain-responsive-layout-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/collection-management-Coll-31097--maintain-responsive-layout-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/collection-management-Coll-31097--maintain-responsive-layout-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/collection-management-Coll-31097--maintain-responsive-layout-chromium/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="feedback-management.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="chromium" tests="16" failures="3" skipped="0" time="80.88" errors="0">
<testcase name="Feedback Management › should display feedback management page correctly" classname="feedback-management.spec.ts" time="3.692">
</testcase>
<testcase name="Feedback Management › should display feedback list" classname="feedback-management.spec.ts" time="5.031">
<failure message="feedback-management.spec.ts:25:6 should display feedback list" type="FAILURE">
<![CDATA[  [chromium] › feedback-management.spec.ts:25:6 › Feedback Management › should display feedback list 

    Error: locator.isVisible: Error: strict mode violation: locator('text=pending').or(locator('text=resolved')) resolved to 10 elements:
        1) <p class="text-sm text-gray-600 dark:text-gray-400">Pending</p> aka getByRole('paragraph').filter({ hasText: 'Pending' })
        2) <p class="text-sm text-gray-600 dark:text-gray-400">Resolved</p> aka getByText('Resolved')
        3) <span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent [a&]:hover:bg-primary/90 bg-yellow-100 text-yellow-800 …>pending</span> aka getByText('pending', { exact: true }).first()
        4) <span data-slot="select-value">Pending</span> aka getByRole('group').filter({ hasText: 'pending7/21/2025, 2:49:31' }).getByRole('combobox')
        5) <span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent [a&]:hover:bg-primary/90 bg-yellow-100 text-yellow-800 …>pending</span> aka getByText('pending', { exact: true }).nth(1)
        6) <span data-slot="select-value">Pending</span> aka getByRole('group').filter({ hasText: 'pending7/21/2025, 2:49:24' }).getByRole('combobox')
        7) <span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent [a&]:hover:bg-primary/90 bg-yellow-100 text-yellow-800 …>pending</span> aka getByText('pending', { exact: true }).nth(2)
        8) <span data-slot="select-value">Pending</span> aka getByRole('group').filter({ hasText: 'pending7/21/2025, 1:52:35' }).getByRole('combobox')
        9) <span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent [a&]:hover:bg-primary/90 bg-yellow-100 text-yellow-800 …>pending</span> aka getByText('pending', { exact: true }).nth(3)
        10) <span data-slot="select-value">Pending</span> aka getByRole('group').filter({ hasText: 'pending7/20/2025, 3:23:24' }).getByRole('combobox')

    Call log:
        - checking visibility of locator('text=pending').or(locator('text=resolved'))


      28 | 		
      29 | 		// Check if feedback items are displayed or empty state
    > 30 | 		const hasFeedback = await page.locator('text=pending').or(page.locator('text=resolved')).isVisible();
         | 		                                                                                         ^
      31 | 		const hasEmptyState = await page.locator('text=No feedback').or(page.locator('text=Empty')).isVisible();
      32 | 		
      33 | 		expect(hasFeedback || hasEmptyState).toBe(true);
        at /Users/<USER>/Github/vocab/e2e/admin/feedback-management.spec.ts:30:92

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/feedback-management-Feedba-b802f-hould-display-feedback-list-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/feedback-management-Feedba-b802f-hould-display-feedback-list-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/feedback-management-Feedba-b802f-hould-display-feedback-list-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/feedback-management-Feedba-b802f-hould-display-feedback-list-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/feedback-management-Feedba-b802f-hould-display-feedback-list-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/feedback-management-Feedba-b802f-hould-display-feedback-list-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Feedback Management › should display feedback status badges" classname="feedback-management.spec.ts" time="5.158">
</testcase>
<testcase name="Feedback Management › should handle feedback status updates" classname="feedback-management.spec.ts" time="4.244">
</testcase>
<testcase name="Feedback Management › should display feedback messages" classname="feedback-management.spec.ts" time="4.314">
</testcase>
<testcase name="Feedback Management › should handle feedback filtering" classname="feedback-management.spec.ts" time="4.979">
</testcase>
<testcase name="Feedback Management › should display feedback timestamps" classname="feedback-management.spec.ts" time="5.903">
</testcase>
<testcase name="Feedback Management › should handle refresh functionality" classname="feedback-management.spec.ts" time="6.874">
</testcase>
<testcase name="Feedback Management › should display user information for feedback" classname="feedback-management.spec.ts" time="5.951">
<failure message="feedback-management.spec.ts:182:6 should display user information for feedback" type="FAILURE">
<![CDATA[  [chromium] › feedback-management.spec.ts:182:6 › Feedback Management › should display user information for feedback 

    Error: locator.isVisible: Error: strict mode violation: locator('text=User').or(locator('text=From:')) resolved to 7 elements:
        1) <span class="font-medium">Users</span> aka getByRole('button', { name: 'Users Manage user accounts' })
        2) <span class="text-xs opacity-75">Manage user accounts</span> aka getByRole('button', { name: 'Users Manage user accounts' })
        3) <span class="text-xs opacity-75">User feedback management</span> aka getByRole('button', { name: 'Feedback User feedback' })
        4) <strong>User:</strong> aka getByRole('group').filter({ hasText: 'pending7/21/2025, 2:49:31' }).getByRole('strong')
        5) <strong>User:</strong> aka getByRole('group').filter({ hasText: 'pending7/21/2025, 2:49:24' }).getByRole('strong')
        6) <strong>User:</strong> aka getByRole('group').filter({ hasText: 'pending7/21/2025, 1:52:35' }).getByRole('strong')
        7) <strong>User:</strong> aka getByRole('group').filter({ hasText: 'pending7/20/2025, 3:23:24' }).getByRole('strong')

    Call log:
        - checking visibility of locator('text=User').or(locator('text=From:'))


      187 | 		const userInfo = page.locator('text=User').or(page.locator('text=From:'));
      188 | 		
    > 189 | 		if (await userInfo.isVisible()) {
          | 		                   ^
      190 | 			await expect(userInfo).toBeVisible();
      191 | 		}
      192 | 	});
        at /Users/<USER>/Github/vocab/e2e/admin/feedback-management.spec.ts:189:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/feedback-management-Feedba-69cea-er-information-for-feedback-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/feedback-management-Feedba-69cea-er-information-for-feedback-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/feedback-management-Feedba-69cea-er-information-for-feedback-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/feedback-management-Feedba-69cea-er-information-for-feedback-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/feedback-management-Feedba-69cea-er-information-for-feedback-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/feedback-management-Feedba-69cea-er-information-for-feedback-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Feedback Management › should handle pagination if available" classname="feedback-management.spec.ts" time="5.266">
</testcase>
<testcase name="Feedback Management › should display feedback count information" classname="feedback-management.spec.ts" time="4.651">
</testcase>
<testcase name="Feedback Management › should handle empty feedback state" classname="feedback-management.spec.ts" time="4.913">
<failure message="feedback-management.spec.ts:231:6 should handle empty feedback state" type="FAILURE">
<![CDATA[  [chromium] › feedback-management.spec.ts:231:6 › Feedback Management › should handle empty feedback state 

    Error: locator.isVisible: Error: strict mode violation: locator('text=pending').or(locator('text=resolved')) resolved to 10 elements:
        1) <p class="text-sm text-gray-600 dark:text-gray-400">Pending</p> aka getByRole('paragraph').filter({ hasText: 'Pending' })
        2) <p class="text-sm text-gray-600 dark:text-gray-400">Resolved</p> aka getByText('Resolved')
        3) <span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent [a&]:hover:bg-primary/90 bg-yellow-100 text-yellow-800 …>pending</span> aka getByText('pending', { exact: true }).first()
        4) <span data-slot="select-value">Pending</span> aka getByRole('group').filter({ hasText: 'pending7/21/2025, 2:49:31' }).getByRole('combobox')
        5) <span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent [a&]:hover:bg-primary/90 bg-yellow-100 text-yellow-800 …>pending</span> aka getByText('pending', { exact: true }).nth(1)
        6) <span data-slot="select-value">Pending</span> aka getByRole('group').filter({ hasText: 'pending7/21/2025, 2:49:24' }).getByRole('combobox')
        7) <span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent [a&]:hover:bg-primary/90 bg-yellow-100 text-yellow-800 …>pending</span> aka getByText('pending', { exact: true }).nth(2)
        8) <span data-slot="select-value">Pending</span> aka getByRole('group').filter({ hasText: 'pending7/21/2025, 1:52:35' }).getByRole('combobox')
        9) <span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent [a&]:hover:bg-primary/90 bg-yellow-100 text-yellow-800 …>pending</span> aka getByText('pending', { exact: true }).nth(3)
        10) <span data-slot="select-value">Pending</span> aka getByRole('group').filter({ hasText: 'pending7/20/2025, 3:23:24' }).getByRole('combobox')

    Call log:
        - checking visibility of locator('text=pending').or(locator('text=resolved'))


      235 | 		// Check if empty state is shown
      236 | 		const emptyState = page.locator('text=No feedback').or(page.locator('text=Empty'));
    > 237 | 		const hasFeedback = await page.locator('text=pending').or(page.locator('text=resolved')).isVisible();
          | 		                                                                                         ^
      238 | 		
      239 | 		if (!hasFeedback) {
      240 | 			// Should show empty state message
        at /Users/<USER>/Github/vocab/e2e/admin/feedback-management.spec.ts:237:92

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/feedback-management-Feedba-abe91-handle-empty-feedback-state-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/feedback-management-Feedba-abe91-handle-empty-feedback-state-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/feedback-management-Feedba-abe91-handle-empty-feedback-state-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/feedback-management-Feedba-abe91-handle-empty-feedback-state-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/feedback-management-Feedba-abe91-handle-empty-feedback-state-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/feedback-management-Feedba-abe91-handle-empty-feedback-state-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Feedback Management › should display feedback actions" classname="feedback-management.spec.ts" time="5.489">
</testcase>
<testcase name="Feedback Management › should handle loading states" classname="feedback-management.spec.ts" time="6.104">
</testcase>
<testcase name="Feedback Management › should maintain responsive layout" classname="feedback-management.spec.ts" time="4.882">
</testcase>
<testcase name="Feedback Management › should handle feedback search functionality" classname="feedback-management.spec.ts" time="3.429">
</testcase>
</testsuite>
<testsuite name="system-monitoring.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="chromium" tests="14" failures="8" skipped="0" time="76.701" errors="0">
<testcase name="System Monitoring › should display system monitoring page correctly" classname="system-monitoring.spec.ts" time="3.92">
</testcase>
<testcase name="System Monitoring › should display system health overview" classname="system-monitoring.spec.ts" time="3.878">
<failure message="system-monitoring.spec.ts:27:6 should display system health overview" type="FAILURE">
<![CDATA[  [chromium] › system-monitoring.spec.ts:27:6 › System Monitoring › should display system health overview 

    Error: expect.toBeVisible: Error: strict mode violation: locator('text=System Health') resolved to 2 elements:
        1) <p class="text-gray-600 dark:text-gray-400 mt-1">Monitor system health and performance</p> aka getByText('Monitor system health and')
        2) <h3 class="font-semibold leading-none tracking-tight flex items-center">…</h3> aka getByRole('heading', { name: 'System Health' })

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=System Health')


      30 | 		
      31 | 		// Check system health section
    > 32 | 		await expect(page.locator('text=System Health')).toBeVisible();
         | 		                                                 ^
      33 | 		
      34 | 		// Check health status badge
      35 | 		const healthBadge = page.locator('[class*="bg-green-100"], [class*="bg-yellow-100"], [class*="bg-red-100"]').first();
        at /Users/<USER>/Github/vocab/e2e/admin/system-monitoring.spec.ts:32:52

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/system-monitoring-System-M-212e8-play-system-health-overview-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/system-monitoring-System-M-212e8-play-system-health-overview-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/system-monitoring-System-M-212e8-play-system-health-overview-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/system-monitoring-System-M-212e8-play-system-health-overview-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/system-monitoring-System-M-212e8-play-system-health-overview-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/system-monitoring-System-M-212e8-play-system-health-overview-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="System Monitoring › should display system components status" classname="system-monitoring.spec.ts" time="3.69">
<failure message="system-monitoring.spec.ts:42:6 should display system components status" type="FAILURE">
<![CDATA[  [chromium] › system-monitoring.spec.ts:42:6 › System Monitoring › should display system components status 

    Error: expect.toBeVisible: Error: strict mode violation: locator('text=Cache') resolved to 2 elements:
        1) <button id="_r_1j_" tabindex="0" role="button" data-slot="button" aria-disabled="false" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40…>…</button> aka getByRole('button', { name: 'Clear Cache' })
        2) <div class="font-medium">Cache</div> aka getByText('Cache', { exact: true })

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Cache')


      48 | 		
      49 | 		// Check cache status
    > 50 | 		await expect(page.locator('text=Cache')).toBeVisible();
         | 		                                         ^
      51 | 		
      52 | 		// Check AI services status
      53 | 		await expect(page.locator('text=AI Services')).toBeVisible();
        at /Users/<USER>/Github/vocab/e2e/admin/system-monitoring.spec.ts:50:44

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/system-monitoring-System-M-3ae6c-ay-system-components-status-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/system-monitoring-System-M-3ae6c-ay-system-components-status-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/system-monitoring-System-M-3ae6c-ay-system-components-status-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/system-monitoring-System-M-3ae6c-ay-system-components-status-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/system-monitoring-System-M-3ae6c-ay-system-components-status-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/system-monitoring-System-M-3ae6c-ay-system-components-status-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="System Monitoring › should display AI token usage statistics" classname="system-monitoring.spec.ts" time="2.783">
</testcase>
<testcase name="System Monitoring › should display recent errors section" classname="system-monitoring.spec.ts" time="4.385">
<failure message="system-monitoring.spec.ts:81:6 should display recent errors section" type="FAILURE">
<![CDATA[  [chromium] › system-monitoring.spec.ts:81:6 › System Monitoring › should display recent errors section 

    Error: expect.toBeVisible: Error: strict mode violation: locator('text=Recent Errors') resolved to 2 elements:
        1) <h3 class="font-semibold leading-none tracking-tight flex items-center">…</h3> aka getByRole('heading', { name: 'Recent Errors' })
        2) <p class="text-gray-600 dark:text-gray-400">No recent errors found</p> aka getByText('No recent errors found')

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Recent Errors')


      84 | 		
      85 | 		// Check recent errors section
    > 86 | 		await expect(page.locator('text=Recent Errors')).toBeVisible();
         | 		                                                 ^
      87 | 		
      88 | 		// Either show errors or "no recent errors" message
      89 | 		const hasErrors = await page.locator('[class*="bg-red-50"]').isVisible();
        at /Users/<USER>/Github/vocab/e2e/admin/system-monitoring.spec.ts:86:52

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/system-monitoring-System-M-8863d-splay-recent-errors-section-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/system-monitoring-System-M-8863d-splay-recent-errors-section-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/system-monitoring-System-M-8863d-splay-recent-errors-section-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/system-monitoring-System-M-8863d-splay-recent-errors-section-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/system-monitoring-System-M-8863d-splay-recent-errors-section-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/system-monitoring-System-M-8863d-splay-recent-errors-section-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="System Monitoring › should handle cache clearing functionality" classname="system-monitoring.spec.ts" time="5.878">
</testcase>
<testcase name="System Monitoring › should refresh system data" classname="system-monitoring.spec.ts" time="5.375">
<failure message="system-monitoring.spec.ts:115:6 should refresh system data" type="FAILURE">
<![CDATA[  [chromium] › system-monitoring.spec.ts:115:6 › System Monitoring › should refresh system data ────

    Error: expect.toBeVisible: Error: strict mode violation: locator('text=System Health') resolved to 2 elements:
        1) <p class="text-gray-600 dark:text-gray-400 mt-1">Monitor system health and performance</p> aka getByText('Monitor system health and')
        2) <h3 class="font-semibold leading-none tracking-tight flex items-center">…</h3> aka getByRole('heading', { name: 'System Health' })

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=System Health')


      125 | 		
      126 | 		// Verify data is still displayed
    > 127 | 		await expect(page.locator('text=System Health')).toBeVisible();
          | 		                                                 ^
      128 | 		await expect(page.locator('text=AI Token Usage')).toBeVisible();
      129 | 	});
      130 |
        at /Users/<USER>/Github/vocab/e2e/admin/system-monitoring.spec.ts:127:52

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/system-monitoring-System-M-688ed--should-refresh-system-data-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/system-monitoring-System-M-688ed--should-refresh-system-data-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/system-monitoring-System-M-688ed--should-refresh-system-data-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/system-monitoring-System-M-688ed--should-refresh-system-data-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/system-monitoring-System-M-688ed--should-refresh-system-data-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/system-monitoring-System-M-688ed--should-refresh-system-data-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="System Monitoring › should display health status icons correctly" classname="system-monitoring.spec.ts" time="9.774">
<failure message="system-monitoring.spec.ts:131:6 should display health status icons correctly" type="FAILURE">
<![CDATA[  [chromium] › system-monitoring.spec.ts:131:6 › System Monitoring › should display health status icons correctly 

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('text=Database').locator('..').locator('svg')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Database').locator('..').locator('svg')


      138 | 		
      139 | 		// Check component icons
    > 140 | 		await expect(page.locator('text=Database').locator('..').locator('svg')).toBeVisible();
          | 		                                                                         ^
      141 | 		await expect(page.locator('text=Cache').locator('..').locator('svg')).toBeVisible();
      142 | 		await expect(page.locator('text=AI Services').locator('..').locator('svg')).toBeVisible();
      143 | 	});
        at /Users/<USER>/Github/vocab/e2e/admin/system-monitoring.spec.ts:140:76

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/system-monitoring-System-M-3407c-alth-status-icons-correctly-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/system-monitoring-System-M-3407c-alth-status-icons-correctly-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/system-monitoring-System-M-3407c-alth-status-icons-correctly-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/system-monitoring-System-M-3407c-alth-status-icons-correctly-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/system-monitoring-System-M-3407c-alth-status-icons-correctly-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/system-monitoring-System-M-3407c-alth-status-icons-correctly-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="System Monitoring › should display token usage with proper formatting" classname="system-monitoring.spec.ts" time="4.934">
</testcase>
<testcase name="System Monitoring › should handle loading states properly" classname="system-monitoring.spec.ts" time="4.039">
<failure message="system-monitoring.spec.ts:162:6 should handle loading states properly" type="FAILURE">
<![CDATA[  [chromium] › system-monitoring.spec.ts:162:6 › System Monitoring › should handle loading states properly 

    Error: expect.toBeVisible: Error: strict mode violation: locator('text=System Health') resolved to 2 elements:
        1) <p class="text-gray-600 dark:text-gray-400 mt-1">Monitor system health and performance</p> aka getByText('Monitor system health and')
        2) <h3 class="font-semibold leading-none tracking-tight flex items-center">…</h3> aka getByRole('heading', { name: 'System Health' })

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=System Health')


      172 | 		
      173 | 		// All sections should be visible
    > 174 | 		await expect(page.locator('text=System Health')).toBeVisible();
          | 		                                                 ^
      175 | 		await expect(page.locator('text=AI Token Usage')).toBeVisible();
      176 | 		await expect(page.locator('text=Recent Errors')).toBeVisible();
      177 | 	});
        at /Users/<USER>/Github/vocab/e2e/admin/system-monitoring.spec.ts:174:52

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/system-monitoring-System-M-32327-dle-loading-states-properly-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/system-monitoring-System-M-32327-dle-loading-states-properly-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/system-monitoring-System-M-32327-dle-loading-states-properly-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/system-monitoring-System-M-32327-dle-loading-states-properly-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/system-monitoring-System-M-32327-dle-loading-states-properly-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/system-monitoring-System-M-32327-dle-loading-states-properly-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="System Monitoring › should display error details when errors exist" classname="system-monitoring.spec.ts" time="8.472">
<failure message="system-monitoring.spec.ts:179:6 should display error details when errors exist" type="FAILURE">
<![CDATA[  [chromium] › system-monitoring.spec.ts:179:6 › System Monitoring › should display error details when errors exist 

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[class*="bg-red-50"]').first().locator('[class*="font-medium"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[class*="bg-red-50"]').first().locator('[class*="font-medium"]')


      192 | 			
      193 | 			// Should have error message
    > 194 | 			await expect(firstError.locator('[class*="font-medium"]')).toBeVisible();
          | 			                                                           ^
      195 | 			
      196 | 			// Should have timestamp
      197 | 			await expect(firstError.locator('text=:')).toBeVisible(); // Time format indicator
        at /Users/<USER>/Github/vocab/e2e/admin/system-monitoring.spec.ts:194:63

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/system-monitoring-System-M-e0f54-r-details-when-errors-exist-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/system-monitoring-System-M-e0f54-r-details-when-errors-exist-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/system-monitoring-System-M-e0f54-r-details-when-errors-exist-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/system-monitoring-System-M-e0f54-r-details-when-errors-exist-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/system-monitoring-System-M-e0f54-r-details-when-errors-exist-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/system-monitoring-System-M-e0f54-r-details-when-errors-exist-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="System Monitoring › should handle cache clearing loading state" classname="system-monitoring.spec.ts" time="7.191">
</testcase>
<testcase name="System Monitoring › should maintain responsive layout" classname="system-monitoring.spec.ts" time="5.504">
<failure message="system-monitoring.spec.ts:222:6 should maintain responsive layout" type="FAILURE">
<![CDATA[  [chromium] › system-monitoring.spec.ts:222:6 › System Monitoring › should maintain responsive layout 

    Error: expect.toBeVisible: Error: strict mode violation: locator('text=System Health') resolved to 2 elements:
        1) <p class="text-gray-600 dark:text-gray-400 mt-1">Monitor system health and performance</p> aka getByText('Monitor system health and')
        2) <h3 class="font-semibold leading-none tracking-tight flex items-center">…</h3> aka getByRole('heading', { name: 'System Health' })

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=System Health')


      233 | 		
      234 | 		// Should still show all sections
    > 235 | 		await expect(page.locator('text=System Health')).toBeVisible();
          | 		                                                 ^
      236 | 		await expect(page.locator('text=AI Token Usage')).toBeVisible();
      237 | 	});
      238 |
        at /Users/<USER>/Github/vocab/e2e/admin/system-monitoring.spec.ts:235:52

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/system-monitoring-System-M-cd5f2--maintain-responsive-layout-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/system-monitoring-System-M-cd5f2--maintain-responsive-layout-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/system-monitoring-System-M-cd5f2--maintain-responsive-layout-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/system-monitoring-System-M-cd5f2--maintain-responsive-layout-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/system-monitoring-System-M-cd5f2--maintain-responsive-layout-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/system-monitoring-System-M-cd5f2--maintain-responsive-layout-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="System Monitoring › should handle error state gracefully" classname="system-monitoring.spec.ts" time="6.878">
</testcase>
</testsuite>
<testsuite name="user-management.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="chromium" tests="12" failures="5" skipped="0" time="64.297" errors="0">
<testcase name="User Management › should display user management page correctly" classname="user-management.spec.ts" time="5.283">
</testcase>
<testcase name="User Management › should display users table with correct columns" classname="user-management.spec.ts" time="5.377">
</testcase>
<testcase name="User Management › should display user data in table rows" classname="user-management.spec.ts" time="5.009">
<failure message="user-management.spec.ts:47:6 should display user data in table rows" type="FAILURE">
<![CDATA[  [chromium] › user-management.spec.ts:47:6 › User Management › should display user data in table rows 

    Error: expect.toBeVisible: Error: strict mode violation: locator('text=admin') resolved to 7 elements:
        1) <span class="text-xl font-bold text-gray-900 dark:text-white">Admin Panel</span> aka getByText('Admin Panel')
        2) <span class="text-xs opacity-75">Admin settings</span> aka getByRole('button', { name: 'Settings Admin settings' })
        3) <div class="text-sm text-gray-600 dark:text-gray-400">Admin Dashboard</div> aka getByText('Admin Dashboard')
        4) <span class="font-medium">superadmin</span> aka getByText('superadmin')
        5) <span data-slot="select-value">Admin</span> aka getByRole('row', { name: 'superadmin USERNAME_PASSWORD' }).getByRole('combobox')
        6) <span class="font-medium">admin</span> aka getByText('admin', { exact: true })
        7) <span data-slot="select-value">Admin</span> aka getByRole('row', { name: 'admin USERNAME_PASSWORD Active 7/21/2025', exact: true }).getByRole('combobox')

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=admin')


      54 | 		
      55 | 		// Check for admin user (should exist from seeding)
    > 56 | 		await expect(page.locator('text=admin')).toBeVisible();
         | 		                                         ^
      57 | 		
      58 | 		// Check for role badges
      59 | 		await expect(page.locator('text=ADMIN').or(page.locator('text=USER'))).toBeVisible();
        at /Users/<USER>/Github/vocab/e2e/admin/user-management.spec.ts:56:44

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/user-management-User-Manag-618b8-lay-user-data-in-table-rows-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/user-management-User-Manag-618b8-lay-user-data-in-table-rows-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/user-management-User-Manag-618b8-lay-user-data-in-table-rows-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/user-management-User-Manag-618b8-lay-user-data-in-table-rows-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/user-management-User-Manag-618b8-lay-user-data-in-table-rows-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/user-management-User-Manag-618b8-lay-user-data-in-table-rows-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="User Management › should search users successfully" classname="user-management.spec.ts" time="5.457">
<failure message="user-management.spec.ts:65:6 should search users successfully" type="FAILURE">
<![CDATA[  [chromium] › user-management.spec.ts:65:6 › User Management › should search users successfully ───

    TypeError: page.click(...).filter is not a function

      69 | 		// Search for admin user
      70 | 		await page.fill('input[placeholder="Search users..."]', 'admin');
    > 71 | 		await page.click('button').filter({ hasText: 'Search' });
         | 		                           ^
      72 | 		
      73 | 		// Wait for search results
      74 | 		await page.waitForTimeout(1000);
        at /Users/<USER>/Github/vocab/e2e/admin/user-management.spec.ts:71:30

    Error: page.click: Test ended.
    Call log:
      - waiting for locator('button')
        - locator resolved to 31 elements. Proceeding with the first one: <button id="_r_d_" tabindex="0" role="button" data-slot="button" aria-disabled="false" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:bord…>…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is not visible
        - retrying click action
        - waiting 20ms
        - waiting for element to be visible, enabled and stable


      69 | 		// Search for admin user
      70 | 		await page.fill('input[placeholder="Search users..."]', 'admin');
    > 71 | 		await page.click('button').filter({ hasText: 'Search' });
         | 		           ^
      72 | 		
      73 | 		// Wait for search results
      74 | 		await page.waitForTimeout(1000);
        at /Users/<USER>/Github/vocab/e2e/admin/user-management.spec.ts:71:14

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/user-management-User-Manag-79f24-d-search-users-successfully-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/user-management-User-Manag-79f24-d-search-users-successfully-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/user-management-User-Manag-79f24-d-search-users-successfully-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/user-management-User-Manag-79f24-d-search-users-successfully-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="User Management › should update user role successfully" classname="user-management.spec.ts" time="4.645">
<failure message="user-management.spec.ts:80:6 should update user role successfully" type="FAILURE">
<![CDATA[  [chromium] › user-management.spec.ts:80:6 › User Management › should update user role successfully 

    Error: locator.click: Error: strict mode violation: locator('text=User') resolved to 26 elements:
        1) <span class="font-medium">Users</span> aka locator('[id="_r_21_"]')
        2) <span class="text-xs opacity-75">Manage user accounts</span> aka locator('[id="_r_21_"]')
        3) <span class="text-xs opacity-75">User feedback management</span> aka locator('[id="_r_23_"]')
        4) <h1 class="text-3xl font-bold text-gray-900 dark:text-white">User Management</h1> aka getByText('User Management')
        5) <p class="text-gray-600 dark:text-gray-400 mt-1">Manage user accounts and permissions</p> aka getByText('Manage user accounts and')
        6) <h3 class="font-semibold leading-none tracking-tight flex items-center">…</h3> aka getByText('Users (8)')
        7) <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">User</th> aka locator('th').filter({ hasText: 'User' })
        8) <span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground">USERNAME_PASSWORD</span> aka locator('tr').filter({ hasText: 'invalidUSERNAME_PASSWORDUserActive7/21/' }).locator('span').nth(1)
        9) <span data-slot="select-value">User</span> aka locator('tr').filter({ hasText: 'invalidUSERNAME_PASSWORDUserActive7/21/' }).locator('button').first()
        10) <span class="font-medium">testuser3</span> aka getByText('testuser3')
        ...

    Call log:
      - waiting for locator('text=User')


      100 | 				await page.locator('text=Admin').click();
      101 | 			} else {
    > 102 | 				await page.locator('text=User').click();
          | 				                                ^
      103 | 			}
      104 | 			
      105 | 			// Wait for update to complete
        at /Users/<USER>/Github/vocab/e2e/admin/user-management.spec.ts:102:37

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/user-management-User-Manag-d54d4-date-user-role-successfully-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/user-management-User-Manag-d54d4-date-user-role-successfully-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/user-management-User-Manag-d54d4-date-user-role-successfully-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/user-management-User-Manag-d54d4-date-user-role-successfully-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/user-management-User-Manag-d54d4-date-user-role-successfully-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/user-management-User-Manag-d54d4-date-user-role-successfully-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="User Management › should toggle user status successfully" classname="user-management.spec.ts" time="6.991">
</testcase>
<testcase name="User Management › should refresh user data" classname="user-management.spec.ts" time="5.865">
<failure message="user-management.spec.ts:148:6 should refresh user data" type="FAILURE">
<![CDATA[  [chromium] › user-management.spec.ts:148:6 › User Management › should refresh user data ──────────

    Error: expect.toBeVisible: Error: strict mode violation: locator('tbody tr') resolved to 8 elements:
        1) <tr class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800">…</tr> aka getByRole('row', { name: 'invalid USERNAME_PASSWORD' })
        2) <tr class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800">…</tr> aka getByRole('row', { name: 'testuser3 USERNAME_PASSWORD' })
        3) <tr class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800">…</tr> aka getByRole('row', { name: 'testuser2 USERNAME_PASSWORD' })
        4) <tr class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800">…</tr> aka getByRole('row', { name: 'testuser1 USERNAME_PASSWORD' })
        5) <tr class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800">…</tr> aka getByRole('row', { name: 'superadmin USERNAME_PASSWORD' })
        6) <tr class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800">…</tr> aka getByRole('row', { name: 'admin USERNAME_PASSWORD Active 7/21/2025', exact: true })
        7) <tr class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800">…</tr> aka getByRole('row', { name: 'testuser USERNAME_PASSWORD' })
        8) <tr class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800">…</tr> aka getByRole('row', { name: 'CT030343 USERNAME_PASSWORD' })

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('tbody tr')


      159 | 		// Verify table is still visible
      160 | 		await expect(page.locator('table')).toBeVisible();
    > 161 | 		await expect(page.locator('tbody tr')).toBeVisible();
          | 		                                       ^
      162 | 	});
      163 |
      164 | 	test('should handle pagination if available', async ({ page }) => {
        at /Users/<USER>/Github/vocab/e2e/admin/user-management.spec.ts:161:42

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/user-management-User-Management-should-refresh-user-data-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/user-management-User-Management-should-refresh-user-data-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/user-management-User-Management-should-refresh-user-data-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/user-management-User-Management-should-refresh-user-data-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/user-management-User-Management-should-refresh-user-data-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/user-management-User-Management-should-refresh-user-data-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="User Management › should handle pagination if available" classname="user-management.spec.ts" time="4.324">
</testcase>
<testcase name="User Management › should display user count information" classname="user-management.spec.ts" time="6.406">
</testcase>
<testcase name="User Management › should handle empty search results" classname="user-management.spec.ts" time="6.024">
<failure message="user-management.spec.ts:204:6 should handle empty search results" type="FAILURE">
<![CDATA[  [chromium] › user-management.spec.ts:204:6 › User Management › should handle empty search results 

    TypeError: page.click(...).filter is not a function

      208 | 		// Search for non-existent user
      209 | 		await page.fill('input[placeholder="Search users..."]', 'nonexistentuser12345');
    > 210 | 		await page.click('button').filter({ hasText: 'Search' });
          | 		                           ^
      211 | 		
      212 | 		// Wait for search to complete
      213 | 		await page.waitForTimeout(1000);
        at /Users/<USER>/Github/vocab/e2e/admin/user-management.spec.ts:210:30

    Error: page.click: Test ended.
    Call log:
      - waiting for locator('button')
        - locator resolved to 31 elements. Proceeding with the first one: <button id="_r_11_" tabindex="0" role="button" data-slot="button" aria-disabled="false" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:bor…>…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is not visible
        - retrying click action
        - waiting 20ms
        - waiting for element to be visible, enabled and stable
        - element is not visible
      - retrying click action
        - waiting 100ms


      208 | 		// Search for non-existent user
      209 | 		await page.fill('input[placeholder="Search users..."]', 'nonexistentuser12345');
    > 210 | 		await page.click('button').filter({ hasText: 'Search' });
          | 		           ^
      211 | 		
      212 | 		// Wait for search to complete
      213 | 		await page.waitForTimeout(1000);
        at /Users/<USER>/Github/vocab/e2e/admin/user-management.spec.ts:210:14

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/user-management-User-Manag-43729-handle-empty-search-results-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/user-management-User-Manag-43729-handle-empty-search-results-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/user-management-User-Manag-43729-handle-empty-search-results-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/user-management-User-Manag-43729-handle-empty-search-results-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="User Management › should display user icons correctly" classname="user-management.spec.ts" time="5.583">
</testcase>
<testcase name="User Management › should handle loading states" classname="user-management.spec.ts" time="3.333">
</testcase>
</testsuite>
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="firefox" tests="8" failures="4" skipped="0" time="67.45" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="2.144">
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="6.288">
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="8.747">
<failure message="admin-auth.spec.ts:61:6 should show error for invalid credentials" type="FAILURE">
<![CDATA[  [firefox] › admin-auth.spec.ts:61:6 › Admin Authentication › should show error for invalid credentials 

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('text=Invalid username or password')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Invalid username or password')


      68 |
      69 | 		// Wait for error message
    > 70 | 		await expect(page.locator('text=Invalid username or password')).toBeVisible();
         | 		                                                                ^
      71 |
      72 | 		// Should still be on login page
      73 | 		await expect(page).toHaveURL('/admin/login');
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:70:67

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-224fa-ror-for-invalid-credentials-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-224fa-ror-for-invalid-credentials-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentic-224fa-ror-for-invalid-credentials-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-224fa-ror-for-invalid-credentials-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-224fa-ror-for-invalid-credentials-firefox/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-224fa-ror-for-invalid-credentials-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="7.674">
<failure message="admin-auth.spec.ts:76:6 should show validation errors for empty fields" type="FAILURE">
<![CDATA[  [firefox] › admin-auth.spec.ts:76:6 › Admin Authentication › should show validation errors for empty fields 

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('text=Please enter both username and password')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Please enter both username and password')


      79 |
      80 | 		// Check for validation messages
    > 81 | 		await expect(page.locator('text=Please enter both username and password')).toBeVisible();
         | 		                                                                           ^
      82 | 	});
      83 |
      84 | 	test('should toggle password visibility', async ({ page }) => {
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:81:78

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-firefox/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="7.777">
<failure message="admin-auth.spec.ts:84:6 should toggle password visibility" type="FAILURE">
<![CDATA[  [firefox] › admin-auth.spec.ts:84:6 › Admin Authentication › should toggle password visibility ───

    Error: Timed out 5000ms waiting for expect(locator).toHaveAttribute(expected)

    Locator: locator('input[id="password"]')
    Expected string: "text"
    Received string: "password"
    Call log:
      - Expect "toHaveAttribute" with timeout 5000ms
      - waiting for locator('input[id="password"]')
        8 × locator resolved to <input value="" required="" id="password" type="password" autocomplete="current-password" placeholder="Enter your password" class="flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 w-full pr-10"/>
          - unexpected value "password"


       98 |
       99 | 		// Password should now be visible
    > 100 | 		await expect(passwordInput).toHaveAttribute('type', 'text');
          | 		                            ^
      101 |
      102 | 		// Click toggle button again
      103 | 		await toggleButton.click();
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:100:31

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-5fc53--toggle-password-visibility-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-5fc53--toggle-password-visibility-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentic-5fc53--toggle-password-visibility-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-5fc53--toggle-password-visibility-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-5fc53--toggle-password-visibility-firefox/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-5fc53--toggle-password-visibility-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="2.83">
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="30.192">
<failure message="admin-auth.spec.ts:125:6 should redirect to login when accessing protected route without auth" type="FAILURE">
<![CDATA[  [firefox] › admin-auth.spec.ts:125:6 › Admin Authentication › should redirect to login when accessing protected route without auth 

    Test timeout of 30000ms exceeded.

    Error: page.waitForURL: Test timeout of 30000ms exceeded.
    =========================== logs ===========================
    waiting for navigation to "/admin/login" until "load"
      navigated to "http://localhost:3000/admin"
    ============================================================

      130 |
      131 | 		// Should redirect to login page
    > 132 | 		await page.waitForURL('/admin/login');
          | 		           ^
      133 | 		await expect(page.locator('text=Admin Login')).toBeVisible();
      134 | 	});
      135 |
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:132:14

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-a0986-rotected-route-without-auth-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-a0986-rotected-route-without-auth-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentic-a0986-rotected-route-without-auth-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-a0986-rotected-route-without-auth-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-a0986-rotected-route-without-auth-firefox/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-a0986-rotected-route-without-auth-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="1.798">
</testcase>
</testsuite>
<testsuite name="admin-dashboard.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="firefox" tests="12" failures="7" skipped="0" time="80.342" errors="0">
<testcase name="Admin Dashboard › should display dashboard overview correctly" classname="admin-dashboard.spec.ts" time="4.836">
<failure message="admin-dashboard.spec.ts:13:6 should display dashboard overview correctly" type="FAILURE">
<![CDATA[  [firefox] › admin-dashboard.spec.ts:13:6 › Admin Dashboard › should display dashboard overview correctly 

    Error: expect.toBeVisible: Error: strict mode violation: locator('text=Collections') resolved to 3 elements:
        1) <span class="font-medium">Collections</span> aka getByRole('button', { name: 'Collections Manage collections' })
        2) <span class="text-xs opacity-75">Manage collections</span> aka getByRole('button', { name: 'Collections Manage collections' })
        3) <h3 class="tracking-tight text-sm font-medium">Collections</h3> aka getByRole('heading', { name: 'Collections' })

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Collections')


      24 | 		// Check stats cards
      25 | 		await expect(page.locator('text=Total Users')).toBeVisible();
    > 26 | 		await expect(page.locator('text=Collections')).toBeVisible();
         | 		                                               ^
      27 | 		await expect(page.locator('text=Feedback')).toBeVisible();
      28 | 		await expect(page.locator('text=Performance')).toBeVisible();
      29 | 	});
        at /Users/<USER>/Github/vocab/e2e/admin/admin-dashboard.spec.ts:26:50

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-41cad-ashboard-overview-correctly-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-41cad-ashboard-overview-correctly-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-dashboard-Admin-Dash-41cad-ashboard-overview-correctly-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-41cad-ashboard-overview-correctly-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-41cad-ashboard-overview-correctly-firefox/video.webm]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-41cad-ashboard-overview-correctly-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Dashboard › should display system health status" classname="admin-dashboard.spec.ts" time="4.22">
<failure message="admin-dashboard.spec.ts:31:6 should display system health status" type="FAILURE">
<![CDATA[  [firefox] › admin-dashboard.spec.ts:31:6 › Admin Dashboard › should display system health status ─

    Error: expect.toBeVisible: Error: strict mode violation: locator('text=Cache') resolved to 2 elements:
        1) <span class="text-gray-600 dark:text-gray-400">Cache:</span> aka getByText('Cache:')
        2) <div class="text-xs text-muted-foreground mt-1">Cache Hit: 85.0%</div> aka getByText('Cache Hit: 85.0%')

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Cache')


      39 | 		// Check health details
      40 | 		await expect(page.locator('text=Database')).toBeVisible();
    > 41 | 		await expect(page.locator('text=Cache')).toBeVisible();
         | 		                                         ^
      42 | 		await expect(page.locator('text=AI Services')).toBeVisible();
      43 | 	});
      44 |
        at /Users/<USER>/Github/vocab/e2e/admin/admin-dashboard.spec.ts:41:44

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-02b98-isplay-system-health-status-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-02b98-isplay-system-health-status-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-dashboard-Admin-Dash-02b98-isplay-system-health-status-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-02b98-isplay-system-health-status-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-02b98-isplay-system-health-status-firefox/video.webm]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-02b98-isplay-system-health-status-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Dashboard › should display user statistics" classname="admin-dashboard.spec.ts" time="7.888">
<failure message="admin-dashboard.spec.ts:45:6 should display user statistics" type="FAILURE">
<![CDATA[  [firefox] › admin-dashboard.spec.ts:45:6 › Admin Dashboard › should display user statistics ──────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('text=Total Users').locator('..').locator('.text-2xl').first()
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Total Users').locator('..').locator('.text-2xl').first()


      53 | 		// Check for numeric values (should be numbers)
      54 | 		const totalUsersValue = page.locator('text=Total Users').locator('..').locator('.text-2xl').first();
    > 55 | 		await expect(totalUsersValue).toBeVisible();
         | 		                              ^
      56 | 		
      57 | 		// Check additional user metrics
      58 | 		await expect(page.locator('text=Active:')).toBeVisible();
        at /Users/<USER>/Github/vocab/e2e/admin/admin-dashboard.spec.ts:55:33

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-5a10e-uld-display-user-statistics-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-5a10e-uld-display-user-statistics-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-dashboard-Admin-Dash-5a10e-uld-display-user-statistics-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-5a10e-uld-display-user-statistics-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-5a10e-uld-display-user-statistics-firefox/video.webm]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-5a10e-uld-display-user-statistics-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Dashboard › should display collection statistics" classname="admin-dashboard.spec.ts" time="3.8">
</testcase>
<testcase name="Admin Dashboard › should display feedback statistics" classname="admin-dashboard.spec.ts" time="4.716">
</testcase>
<testcase name="Admin Dashboard › should display performance metrics" classname="admin-dashboard.spec.ts" time="5.617">
</testcase>
<testcase name="Admin Dashboard › should refresh dashboard data" classname="admin-dashboard.spec.ts" time="3.762">
</testcase>
<testcase name="Admin Dashboard › should navigate to different admin sections" classname="admin-dashboard.spec.ts" time="9.73">
</testcase>
<testcase name="Admin Dashboard › should display sidebar navigation correctly" classname="admin-dashboard.spec.ts" time="5.432">
<failure message="admin-dashboard.spec.ts:148:6 should display sidebar navigation correctly" type="FAILURE">
<![CDATA[  [firefox] › admin-dashboard.spec.ts:148:6 › Admin Dashboard › should display sidebar navigation correctly 

    Error: expect.toBeVisible: Error: strict mode violation: locator('nav').locator('text=Collections') resolved to 2 elements:
        1) <span class="font-medium">Collections</span> aka getByRole('button', { name: 'Collections Manage collections' })
        2) <span class="text-xs opacity-75">Manage collections</span> aka getByRole('button', { name: 'Collections Manage collections' })

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('nav').locator('text=Collections')


      153 | 		await expect(page.locator('nav').locator('text=Dashboard')).toBeVisible();
      154 | 		await expect(page.locator('nav').locator('text=Users')).toBeVisible();
    > 155 | 		await expect(page.locator('nav').locator('text=Collections')).toBeVisible();
          | 		                                                              ^
      156 | 		await expect(page.locator('nav').locator('text=Feedback')).toBeVisible();
      157 | 		await expect(page.locator('nav').locator('text=System')).toBeVisible();
      158 | 		await expect(page.locator('nav').locator('text=Audit Logs')).toBeVisible();
        at /Users/<USER>/Github/vocab/e2e/admin/admin-dashboard.spec.ts:155:65

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-949b6-idebar-navigation-correctly-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-949b6-idebar-navigation-correctly-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-dashboard-Admin-Dash-949b6-idebar-navigation-correctly-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-949b6-idebar-navigation-correctly-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-949b6-idebar-navigation-correctly-firefox/video.webm]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-949b6-idebar-navigation-correctly-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Dashboard › should handle mobile navigation" classname="admin-dashboard.spec.ts" time="14.619">
<failure message="admin-dashboard.spec.ts:164:6 should handle mobile navigation" type="FAILURE">
<![CDATA[  [firefox] › admin-dashboard.spec.ts:164:6 › Admin Dashboard › should handle mobile navigation ────

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('button').filter({ has: locator('svg') }).first()
        - locator resolved to <button id="_r_2_" tabindex="0" role="button" data-slot="button" aria-disabled="false" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:bord…>…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is not stable
        - retrying click action
        - waiting 20ms
        - waiting for element to be visible, enabled and stable
        - element is not stable
      2 × retrying click action
          - waiting 100ms
          - waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
      16 × retrying click action
           - waiting 500ms
           - waiting for element to be visible, enabled and stable
           - element is visible, enabled and stable
           - scrolling into view if needed
           - done scrolling
           - element is outside of the viewport
      - retrying click action
        - waiting 500ms
        - waiting for element to be visible, enabled and stable


      172 | 		// Click mobile menu button if it exists
      173 | 		if (await mobileMenuButton.isVisible()) {
    > 174 | 			await mobileMenuButton.click();
          | 			                       ^
      175 | 			
      176 | 			// Check if navigation becomes visible
      177 | 			await expect(page.locator('nav')).toBeVisible();
        at /Users/<USER>/Github/vocab/e2e/admin/admin-dashboard.spec.ts:174:27

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-7767d-ld-handle-mobile-navigation-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-7767d-ld-handle-mobile-navigation-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-dashboard-Admin-Dash-7767d-ld-handle-mobile-navigation-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-7767d-ld-handle-mobile-navigation-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-7767d-ld-handle-mobile-navigation-firefox/video.webm]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-7767d-ld-handle-mobile-navigation-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Dashboard › should display error state gracefully" classname="admin-dashboard.spec.ts" time="8.767">
<failure message="admin-dashboard.spec.ts:181:6 should display error state gracefully" type="FAILURE">
<![CDATA[  [firefox] › admin-dashboard.spec.ts:181:6 › Admin Dashboard › should display error state gracefully 

    Error: Timed out 5000ms waiting for expect(locator).not.toBeVisible()

    Locator: locator('text=Error')
    Expected: not visible
    Received: visible
    Call log:
      - Expect "not toBeVisible" with timeout 5000ms
      - waiting for locator('text=Error')
        8 × locator resolved to <div class="text-xs text-muted-foreground">Error Rate: 2.00%</div>
          - unexpected value "visible"


      186 | 		// Check that no error messages are displayed on successful load
      187 | 		await expect(page.locator('text=Failed to load')).not.toBeVisible();
    > 188 | 		await expect(page.locator('text=Error')).not.toBeVisible();
          | 		                                             ^
      189 | 	});
      190 |
      191 | 	test('should maintain active navigation state', async ({ page }) => {
        at /Users/<USER>/Github/vocab/e2e/admin/admin-dashboard.spec.ts:188:48

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-2c393-play-error-state-gracefully-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-2c393-play-error-state-gracefully-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-dashboard-Admin-Dash-2c393-play-error-state-gracefully-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-2c393-play-error-state-gracefully-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-2c393-play-error-state-gracefully-firefox/video.webm]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-2c393-play-error-state-gracefully-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Dashboard › should maintain active navigation state" classname="admin-dashboard.spec.ts" time="6.955">
<failure message="admin-dashboard.spec.ts:191:6 should maintain active navigation state" type="FAILURE">
<![CDATA[  [firefox] › admin-dashboard.spec.ts:191:6 › Admin Dashboard › should maintain active navigation state 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('nav').locator('text=Dashboard').locator('..')
    Expected pattern: /bg-blue-600|default/
    Received string:  "flex flex-col items-start"
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('nav').locator('text=Dashboard').locator('..')
        8 × locator resolved to <div class="flex flex-col items-start">…</div>
          - unexpected value "flex flex-col items-start"


      192 | 		// Dashboard should be active initially
      193 | 		const dashboardNav = page.locator('nav').locator('text=Dashboard').locator('..');
    > 194 | 		await expect(dashboardNav).toHaveClass(/bg-blue-600|default/);
          | 		                           ^
      195 | 		
      196 | 		// Navigate to Users
      197 | 		await page.click('text=Users');
        at /Users/<USER>/Github/vocab/e2e/admin/admin-dashboard.spec.ts:194:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-2aa4a-ain-active-navigation-state-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-dashboard-Admin-Dash-2aa4a-ain-active-navigation-state-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-dashboard-Admin-Dash-2aa4a-ain-active-navigation-state-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-2aa4a-ain-active-navigation-state-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-2aa4a-ain-active-navigation-state-firefox/video.webm]]

[[ATTACHMENT|test-results-admin/admin-dashboard-Admin-Dash-2aa4a-ain-active-navigation-state-firefox/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="admin-integration.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="firefox" tests="10" failures="8" skipped="0" time="119.245" errors="0">
<testcase name="Admin Integration Tests › should complete full admin workflow" classname="admin-integration.spec.ts" time="4.904">
<failure message="admin-integration.spec.ts:13:6 should complete full admin workflow" type="FAILURE">
<![CDATA[  [firefox] › admin-integration.spec.ts:13:6 › Admin Integration Tests › should complete full admin workflow 

    TypeError: page.click(...).filter is not a function

      22 | 		// 3. Search for a user
      23 | 		await page.fill('input[placeholder="Search users..."]', 'admin');
    > 24 | 		await page.click('button').filter({ hasText: 'Search' });
         | 		                           ^
      25 | 		await page.waitForTimeout(1000);
      26 | 		
      27 | 		// 4. Navigate to collections
        at /Users/<USER>/Github/vocab/e2e/admin/admin-integration.spec.ts:24:30

    Error: page.click: Test ended.
    Call log:
      - waiting for locator('button')
        - locator resolved to 31 elements. Proceeding with the first one: <button id="_r_d_" tabindex="0" role="button" data-slot="button" aria-disabled="false" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:bord…>…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is not visible
        - retrying click action
        - waiting 20ms
        - waiting for element to be visible, enabled and stable


      22 | 		// 3. Search for a user
      23 | 		await page.fill('input[placeholder="Search users..."]', 'admin');
    > 24 | 		await page.click('button').filter({ hasText: 'Search' });
         | 		           ^
      25 | 		await page.waitForTimeout(1000);
      26 | 		
      27 | 		// 4. Navigate to collections
        at /Users/<USER>/Github/vocab/e2e/admin/admin-integration.spec.ts:24:14

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-31bb0-omplete-full-admin-workflow-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-integration-Admin-In-31bb0-omplete-full-admin-workflow-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-31bb0-omplete-full-admin-workflow-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-31bb0-omplete-full-admin-workflow-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Integration Tests › should maintain session across navigation" classname="admin-integration.spec.ts" time="30.666">
<failure message="admin-integration.spec.ts:62:6 should maintain session across navigation" type="FAILURE">
<![CDATA[  [firefox] › admin-integration.spec.ts:62:6 › Admin Integration Tests › should maintain session across navigation 

    Test timeout of 30000ms exceeded.

    Error: page.waitForURL: Test timeout of 30000ms exceeded.
    =========================== logs ===========================
    waiting for navigation to "/admin/feedback" until "load"
    ============================================================

      72 | 		for (const adminPage of pages) {
      73 | 			await page.click(`text=${adminPage.link}`);
    > 74 | 			await page.waitForURL(adminPage.url);
         | 			           ^
      75 | 			
      76 | 			// Should not redirect to login (session maintained)
      77 | 			await expect(page).toHaveURL(adminPage.url);
        at /Users/<USER>/Github/vocab/e2e/admin/admin-integration.spec.ts:74:15

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-ab9c4-n-session-across-navigation-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-ab9c4-n-session-across-navigation-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-integration-Admin-In-ab9c4-n-session-across-navigation-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-ab9c4-n-session-across-navigation-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-ab9c4-n-session-across-navigation-firefox/video.webm]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-ab9c4-n-session-across-navigation-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Integration Tests › should handle concurrent admin operations" classname="admin-integration.spec.ts" time="11.063">
<failure message="admin-integration.spec.ts:84:6 should handle concurrent admin operations" type="FAILURE">
<![CDATA[  [firefox] › admin-integration.spec.ts:84:6 › Admin Integration Tests › should handle concurrent admin operations 

    Error: expect.toBeVisible: Error: strict mode violation: locator('text=System Health') resolved to 2 elements:
        1) <p class="text-gray-600 dark:text-gray-400 mt-1">Monitor system health and performance</p> aka getByText('Monitor system health and')
        2) <h3 class="font-semibold leading-none tracking-tight flex items-center">…</h3> aka getByRole('heading', { name: 'System Health' })

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=System Health')


      102 | 		// Page should still be functional
      103 | 		await expect(page.locator('h1')).toContainText('System Monitoring');
    > 104 | 		await expect(page.locator('text=System Health')).toBeVisible();
          | 		                                                 ^
      105 | 	});
      106 |
      107 | 	test('should handle admin logout and re-login', async ({ page }) => {
        at /Users/<USER>/Github/vocab/e2e/admin/admin-integration.spec.ts:104:52

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-87c8a-concurrent-admin-operations-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-87c8a-concurrent-admin-operations-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-integration-Admin-In-87c8a-concurrent-admin-operations-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-87c8a-concurrent-admin-operations-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-87c8a-concurrent-admin-operations-firefox/video.webm]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-87c8a-concurrent-admin-operations-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Integration Tests › should handle admin logout and re-login" classname="admin-integration.spec.ts" time="13.406">
<failure message="admin-integration.spec.ts:107:6 should handle admin logout and re-login" type="FAILURE">
<![CDATA[  [firefox] › admin-integration.spec.ts:107:6 › Admin Integration Tests › should handle admin logout and re-login 

    Error: Timed out 5000ms waiting for expect(locator).toContainText(expected)

    Locator: locator('h1')
    Expected string: "Admin Login"
    Received: <element(s) not found>
    Call log:
      - Expect "toContainText" with timeout 5000ms
      - waiting for locator('h1')


      115 | 		// Should redirect to login
      116 | 		await page.waitForURL('/admin/login');
    > 117 | 		await expect(page.locator('h1')).toContainText('Admin Login');
          | 		                                 ^
      118 | 		
      119 | 		// Try to access protected route
      120 | 		await page.goto('/admin/users');
        at /Users/<USER>/Github/vocab/e2e/admin/admin-integration.spec.ts:117:36

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-28e6f-e-admin-logout-and-re-login-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-28e6f-e-admin-logout-and-re-login-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-integration-Admin-In-28e6f-e-admin-logout-and-re-login-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-28e6f-e-admin-logout-and-re-login-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-28e6f-e-admin-logout-and-re-login-firefox/video.webm]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-28e6f-e-admin-logout-and-re-login-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Integration Tests › should handle browser refresh on admin pages" classname="admin-integration.spec.ts" time="4.773">
</testcase>
<testcase name="Admin Integration Tests › should handle admin operations with error recovery" classname="admin-integration.spec.ts" time="5.746">
<failure message="admin-integration.spec.ts:149:6 should handle admin operations with error recovery" type="FAILURE">
<![CDATA[  [firefox] › admin-integration.spec.ts:149:6 › Admin Integration Tests › should handle admin operations with error recovery 

    TypeError: page.click(...).filter is not a function

      154 | 		// Try to search for non-existent user
      155 | 		await page.fill('input[placeholder="Search users..."]', 'nonexistentuser12345');
    > 156 | 		await page.click('button').filter({ hasText: 'Search' });
          | 		                           ^
      157 | 		await page.waitForTimeout(1000);
      158 | 		
      159 | 		// Clear search and try again
        at /Users/<USER>/Github/vocab/e2e/admin/admin-integration.spec.ts:156:30

    Error: page.click: Test ended.
    Call log:
      - waiting for locator('button')
        - locator resolved to 31 elements. Proceeding with the first one: <button id="_r_b_" tabindex="0" role="button" data-slot="button" aria-disabled="false" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:bord…>…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is not visible
        - retrying click action
        - waiting 20ms
        - waiting for element to be visible, enabled and stable


      154 | 		// Try to search for non-existent user
      155 | 		await page.fill('input[placeholder="Search users..."]', 'nonexistentuser12345');
    > 156 | 		await page.click('button').filter({ hasText: 'Search' });
          | 		           ^
      157 | 		await page.waitForTimeout(1000);
      158 | 		
      159 | 		// Clear search and try again
        at /Users/<USER>/Github/vocab/e2e/admin/admin-integration.spec.ts:156:14

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-4f5f8-rations-with-error-recovery-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-integration-Admin-In-4f5f8-rations-with-error-recovery-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-4f5f8-rations-with-error-recovery-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-4f5f8-rations-with-error-recovery-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Integration Tests › should maintain admin privileges throughout session" classname="admin-integration.spec.ts" time="8.247">
</testcase>
<testcase name="Admin Integration Tests › should handle mobile responsive admin interface" classname="admin-integration.spec.ts" time="15.218">
<failure message="admin-integration.spec.ts:195:6 should handle mobile responsive admin interface" type="FAILURE">
<![CDATA[  [firefox] › admin-integration.spec.ts:195:6 › Admin Integration Tests › should handle mobile responsive admin interface 

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('button').filter({ has: locator('svg') }).first()
        - locator resolved to <button id="_r_2_" tabindex="0" role="button" data-slot="button" aria-disabled="false" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:bord…>…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
        - retrying click action
        - waiting 20ms
        2 × waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
        - retrying click action
          - waiting 100ms
        15 × waiting for element to be visible, enabled and stable
           - element is visible, enabled and stable
           - scrolling into view if needed
           - done scrolling
           - element is outside of the viewport
         - retrying click action
           - waiting 500ms
        - waiting for element to be visible, enabled and stable


      204 | 			const mobileMenuButton = page.locator('button').filter({ has: page.locator('svg') }).first();
      205 | 			if (await mobileMenuButton.isVisible()) {
    > 206 | 				await mobileMenuButton.click();
          | 				                       ^
      207 | 			}
      208 | 			
      209 | 			// Click section
        at /Users/<USER>/Github/vocab/e2e/admin/admin-integration.spec.ts:206:28

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-3f3dd--responsive-admin-interface-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-3f3dd--responsive-admin-interface-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-integration-Admin-In-3f3dd--responsive-admin-interface-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-3f3dd--responsive-admin-interface-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-3f3dd--responsive-admin-interface-firefox/video.webm]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-3f3dd--responsive-admin-interface-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Integration Tests › should handle admin data consistency" classname="admin-integration.spec.ts" time="17.43">
<failure message="admin-integration.spec.ts:224:6 should handle admin data consistency" type="FAILURE">
<![CDATA[  [firefox] › admin-integration.spec.ts:224:6 › Admin Integration Tests › should handle admin data consistency 

    TimeoutError: locator.textContent: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('text=Total Users').locator('..').locator('[class*="text-2xl"]')


      225 | 		// Get initial user count from dashboard
      226 | 		await expect(page.locator('text=Total Users')).toBeVisible();
    > 227 | 		const dashboardUserCount = await page.locator('text=Total Users').locator('..').locator('[class*="text-2xl"]').textContent();
          | 		                                                                                                               ^
      228 | 		
      229 | 		// Navigate to user management
      230 | 		await page.click('text=Users');
        at /Users/<USER>/Github/vocab/e2e/admin/admin-integration.spec.ts:227:114

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-2ab62-ndle-admin-data-consistency-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-2ab62-ndle-admin-data-consistency-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-integration-Admin-In-2ab62-ndle-admin-data-consistency-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-2ab62-ndle-admin-data-consistency-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-2ab62-ndle-admin-data-consistency-firefox/video.webm]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-2ab62-ndle-admin-data-consistency-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Integration Tests › should handle admin error states gracefully" classname="admin-integration.spec.ts" time="7.792">
<failure message="admin-integration.spec.ts:254:6 should handle admin error states gracefully" type="FAILURE">
<![CDATA[  [firefox] › admin-integration.spec.ts:254:6 › Admin Integration Tests › should handle admin error states gracefully 

    Error: expect.toBeVisible: Error: strict mode violation: locator('text=System Health') resolved to 2 elements:
        1) <p class="text-gray-600 dark:text-gray-400 mt-1">Monitor system health and performance</p> aka getByText('Monitor system health and')
        2) <h3 class="font-semibold leading-none tracking-tight flex items-center">…</h3> aka getByRole('heading', { name: 'System Health' })

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=System Health')


      279 | 		} else {
      280 | 			// If no error, should show normal content
    > 281 | 			await expect(page.locator('text=System Health')).toBeVisible();
          | 			                                                 ^
      282 | 		}
      283 | 	});
      284 | });
        at /Users/<USER>/Github/vocab/e2e/admin/admin-integration.spec.ts:281:53

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-2f84e-min-error-states-gracefully-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-integration-Admin-In-2f84e-min-error-states-gracefully-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-integration-Admin-In-2f84e-min-error-states-gracefully-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-2f84e-min-error-states-gracefully-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-2f84e-min-error-states-gracefully-firefox/video.webm]]

[[ATTACHMENT|test-results-admin/admin-integration-Admin-In-2f84e-min-error-states-gracefully-firefox/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="audit-logs.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="firefox" tests="18" failures="2" skipped="16" time="34.266" errors="0">
<testcase name="Audit Logs › should display audit logs page correctly" classname="audit-logs.spec.ts" time="13.033">
<failure message="audit-logs.spec.ts:17:6 should display audit logs page correctly" type="FAILURE">
<![CDATA[  [firefox] › audit-logs.spec.ts:17:6 › Audit Logs › should display audit logs page correctly ──────

    Error: Timed out 5000ms waiting for expect(locator).toContainText(expected)

    Locator: locator('h1')
    Expected string: "Audit Logs"
    Received string: "Something went wrong"
    Call log:
      - Expect "toContainText" with timeout 5000ms
      - waiting for locator('h1')
        5 × locator resolved to <h1 class="text-xl font-semibold text-gray-900 dark:text-white text-center mb-2">Something went wrong</h1>
          - unexpected value "Something went wrong"


      17 | 	test('should display audit logs page correctly', async ({ page }) => {
      18 | 		// Check page title and description
    > 19 | 		await expect(page.locator('h1')).toContainText('Audit Logs');
         | 		                                 ^
      20 | 		await expect(page.locator('text=System activity and security audit trail')).toBeVisible();
      21 | 		
      22 | 		// Check refresh button
        at /Users/<USER>/Github/vocab/e2e/admin/audit-logs.spec.ts:19:36

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/audit-logs-Audit-Logs-shou-158dd-y-audit-logs-page-correctly-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/audit-logs-Audit-Logs-shou-158dd-y-audit-logs-page-correctly-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/audit-logs-Audit-Logs-shou-158dd-y-audit-logs-page-correctly-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-shou-158dd-y-audit-logs-page-correctly-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-shou-158dd-y-audit-logs-page-correctly-firefox/video.webm]]

[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-shou-158dd-y-audit-logs-page-correctly-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Audit Logs › should display filters section" classname="audit-logs.spec.ts" time="9.741">
<failure message="audit-logs.spec.ts:26:6 should display filters section" type="FAILURE">
<![CDATA[  [firefox] › audit-logs.spec.ts:26:6 › Audit Logs › should display filters section ────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('text=Filters')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Filters')


      26 | 	test('should display filters section', async ({ page }) => {
      27 | 		// Check filters section
    > 28 | 		await expect(page.locator('text=Filters')).toBeVisible();
         | 		                                           ^
      29 | 		
      30 | 		// Check filter inputs
      31 | 		await expect(page.locator('input[placeholder*="action"]')).toBeVisible();
        at /Users/<USER>/Github/vocab/e2e/admin/audit-logs.spec.ts:28:46

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/audit-logs-Audit-Logs-should-display-filters-section-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/audit-logs-Audit-Logs-should-display-filters-section-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/audit-logs-Audit-Logs-should-display-filters-section-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-display-filters-section-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-display-filters-section-firefox/video.webm]]

[[ATTACHMENT|test-results-admin/audit-logs-Audit-Logs-should-display-filters-section-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Audit Logs › should display audit events section" classname="audit-logs.spec.ts" time="6.994">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display audit event details" classname="audit-logs.spec.ts" time="3.108">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should filter audit logs by action" classname="audit-logs.spec.ts" time="1.39">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should filter audit logs by resource" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should filter audit logs by user ID" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should clear filters" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display event badges with correct colors" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display event icons correctly" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display event metadata" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should handle pagination if available" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display pagination information" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should refresh audit logs" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should handle empty audit logs state" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display JSON event details" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should handle loading states" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should maintain responsive layout" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="collection-management.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="firefox" tests="15" failures="0" skipped="15" time="0" errors="0">
<testcase name="Collection Management › should display collection management page correctly" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display collections in grid layout" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display collection cards with correct information" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display language badges correctly" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display content statistics" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display collection metadata" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle collection deletion with confirmation" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle view collection action" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should refresh collections data" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle pagination if available" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display pagination information" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle empty state correctly" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display collection icons correctly" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle loading states" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should maintain responsive layout" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="feedback-management.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="firefox" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Feedback Management › should display feedback management page correctly" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback list" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback status badges" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle feedback status updates" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback messages" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle feedback filtering" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback timestamps" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle refresh functionality" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display user information for feedback" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle pagination if available" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback count information" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle empty feedback state" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback actions" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle loading states" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should maintain responsive layout" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle feedback search functionality" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="system-monitoring.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="firefox" tests="14" failures="0" skipped="14" time="0" errors="0">
<testcase name="System Monitoring › should display system monitoring page correctly" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display system health overview" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display system components status" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display AI token usage statistics" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display recent errors section" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should handle cache clearing functionality" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should refresh system data" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display health status icons correctly" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display token usage with proper formatting" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should handle loading states properly" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display error details when errors exist" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should handle cache clearing loading state" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should maintain responsive layout" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should handle error state gracefully" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="user-management.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="firefox" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="User Management › should display user management page correctly" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should display users table with correct columns" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should display user data in table rows" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should search users successfully" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should update user role successfully" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should toggle user status successfully" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should refresh user data" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should handle pagination if available" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should display user count information" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should handle empty search results" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should display user icons correctly" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should handle loading states" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="webkit" tests="8" failures="0" skipped="8" time="0" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin-dashboard.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="webkit" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Admin Dashboard › should display dashboard overview correctly" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display system health status" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display user statistics" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display collection statistics" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display feedback statistics" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display performance metrics" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should refresh dashboard data" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should navigate to different admin sections" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display sidebar navigation correctly" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should handle mobile navigation" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display error state gracefully" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should maintain active navigation state" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin-integration.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="webkit" tests="10" failures="0" skipped="10" time="0" errors="0">
<testcase name="Admin Integration Tests › should complete full admin workflow" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should maintain session across navigation" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle concurrent admin operations" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle admin logout and re-login" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle browser refresh on admin pages" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle admin operations with error recovery" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should maintain admin privileges throughout session" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle mobile responsive admin interface" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle admin data consistency" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle admin error states gracefully" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="audit-logs.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="webkit" tests="18" failures="0" skipped="18" time="0" errors="0">
<testcase name="Audit Logs › should display audit logs page correctly" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display filters section" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display audit events section" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display audit event details" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should filter audit logs by action" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should filter audit logs by resource" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should filter audit logs by user ID" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should clear filters" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display event badges with correct colors" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display event icons correctly" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display event metadata" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should handle pagination if available" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display pagination information" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should refresh audit logs" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should handle empty audit logs state" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display JSON event details" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should handle loading states" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should maintain responsive layout" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="collection-management.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="webkit" tests="15" failures="0" skipped="15" time="0" errors="0">
<testcase name="Collection Management › should display collection management page correctly" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display collections in grid layout" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display collection cards with correct information" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display language badges correctly" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display content statistics" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display collection metadata" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle collection deletion with confirmation" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle view collection action" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should refresh collections data" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle pagination if available" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display pagination information" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle empty state correctly" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display collection icons correctly" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle loading states" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should maintain responsive layout" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="feedback-management.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="webkit" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Feedback Management › should display feedback management page correctly" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback list" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback status badges" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle feedback status updates" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback messages" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle feedback filtering" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback timestamps" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle refresh functionality" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display user information for feedback" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle pagination if available" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback count information" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle empty feedback state" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback actions" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle loading states" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should maintain responsive layout" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle feedback search functionality" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="system-monitoring.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="webkit" tests="14" failures="0" skipped="14" time="0" errors="0">
<testcase name="System Monitoring › should display system monitoring page correctly" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display system health overview" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display system components status" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display AI token usage statistics" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display recent errors section" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should handle cache clearing functionality" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should refresh system data" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display health status icons correctly" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display token usage with proper formatting" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should handle loading states properly" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display error details when errors exist" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should handle cache clearing loading state" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should maintain responsive layout" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should handle error state gracefully" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="user-management.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="webkit" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="User Management › should display user management page correctly" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should display users table with correct columns" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should display user data in table rows" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should search users successfully" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should update user role successfully" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should toggle user status successfully" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should refresh user data" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should handle pagination if available" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should display user count information" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should handle empty search results" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should display user icons correctly" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should handle loading states" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Mobile Chrome" tests="8" failures="0" skipped="8" time="0" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin-dashboard.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Mobile Chrome" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Admin Dashboard › should display dashboard overview correctly" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display system health status" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display user statistics" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display collection statistics" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display feedback statistics" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display performance metrics" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should refresh dashboard data" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should navigate to different admin sections" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display sidebar navigation correctly" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should handle mobile navigation" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display error state gracefully" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should maintain active navigation state" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin-integration.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Mobile Chrome" tests="10" failures="0" skipped="10" time="0" errors="0">
<testcase name="Admin Integration Tests › should complete full admin workflow" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should maintain session across navigation" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle concurrent admin operations" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle admin logout and re-login" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle browser refresh on admin pages" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle admin operations with error recovery" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should maintain admin privileges throughout session" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle mobile responsive admin interface" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle admin data consistency" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle admin error states gracefully" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="audit-logs.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Mobile Chrome" tests="18" failures="0" skipped="18" time="0" errors="0">
<testcase name="Audit Logs › should display audit logs page correctly" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display filters section" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display audit events section" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display audit event details" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should filter audit logs by action" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should filter audit logs by resource" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should filter audit logs by user ID" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should clear filters" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display event badges with correct colors" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display event icons correctly" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display event metadata" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should handle pagination if available" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display pagination information" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should refresh audit logs" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should handle empty audit logs state" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display JSON event details" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should handle loading states" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should maintain responsive layout" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="collection-management.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Mobile Chrome" tests="15" failures="0" skipped="15" time="0" errors="0">
<testcase name="Collection Management › should display collection management page correctly" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display collections in grid layout" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display collection cards with correct information" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display language badges correctly" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display content statistics" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display collection metadata" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle collection deletion with confirmation" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle view collection action" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should refresh collections data" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle pagination if available" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display pagination information" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle empty state correctly" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display collection icons correctly" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle loading states" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should maintain responsive layout" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="feedback-management.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Mobile Chrome" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Feedback Management › should display feedback management page correctly" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback list" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback status badges" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle feedback status updates" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback messages" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle feedback filtering" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback timestamps" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle refresh functionality" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display user information for feedback" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle pagination if available" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback count information" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle empty feedback state" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback actions" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle loading states" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should maintain responsive layout" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle feedback search functionality" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="system-monitoring.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Mobile Chrome" tests="14" failures="0" skipped="14" time="0" errors="0">
<testcase name="System Monitoring › should display system monitoring page correctly" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display system health overview" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display system components status" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display AI token usage statistics" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display recent errors section" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should handle cache clearing functionality" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should refresh system data" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display health status icons correctly" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display token usage with proper formatting" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should handle loading states properly" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display error details when errors exist" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should handle cache clearing loading state" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should maintain responsive layout" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should handle error state gracefully" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="user-management.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Mobile Chrome" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="User Management › should display user management page correctly" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should display users table with correct columns" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should display user data in table rows" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should search users successfully" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should update user role successfully" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should toggle user status successfully" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should refresh user data" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should handle pagination if available" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should display user count information" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should handle empty search results" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should display user icons correctly" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should handle loading states" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Mobile Safari" tests="8" failures="0" skipped="8" time="0" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin-dashboard.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Mobile Safari" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Admin Dashboard › should display dashboard overview correctly" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display system health status" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display user statistics" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display collection statistics" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display feedback statistics" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display performance metrics" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should refresh dashboard data" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should navigate to different admin sections" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display sidebar navigation correctly" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should handle mobile navigation" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display error state gracefully" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should maintain active navigation state" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin-integration.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Mobile Safari" tests="10" failures="0" skipped="10" time="0" errors="0">
<testcase name="Admin Integration Tests › should complete full admin workflow" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should maintain session across navigation" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle concurrent admin operations" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle admin logout and re-login" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle browser refresh on admin pages" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle admin operations with error recovery" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should maintain admin privileges throughout session" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle mobile responsive admin interface" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle admin data consistency" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle admin error states gracefully" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="audit-logs.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Mobile Safari" tests="18" failures="0" skipped="18" time="0" errors="0">
<testcase name="Audit Logs › should display audit logs page correctly" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display filters section" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display audit events section" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display audit event details" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should filter audit logs by action" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should filter audit logs by resource" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should filter audit logs by user ID" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should clear filters" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display event badges with correct colors" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display event icons correctly" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display event metadata" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should handle pagination if available" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display pagination information" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should refresh audit logs" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should handle empty audit logs state" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display JSON event details" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should handle loading states" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should maintain responsive layout" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="collection-management.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Mobile Safari" tests="15" failures="0" skipped="15" time="0" errors="0">
<testcase name="Collection Management › should display collection management page correctly" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display collections in grid layout" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display collection cards with correct information" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display language badges correctly" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display content statistics" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display collection metadata" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle collection deletion with confirmation" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle view collection action" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should refresh collections data" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle pagination if available" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display pagination information" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle empty state correctly" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display collection icons correctly" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle loading states" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should maintain responsive layout" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="feedback-management.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Mobile Safari" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Feedback Management › should display feedback management page correctly" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback list" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback status badges" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle feedback status updates" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback messages" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle feedback filtering" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback timestamps" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle refresh functionality" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display user information for feedback" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle pagination if available" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback count information" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle empty feedback state" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback actions" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle loading states" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should maintain responsive layout" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle feedback search functionality" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="system-monitoring.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Mobile Safari" tests="14" failures="0" skipped="14" time="0" errors="0">
<testcase name="System Monitoring › should display system monitoring page correctly" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display system health overview" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display system components status" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display AI token usage statistics" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display recent errors section" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should handle cache clearing functionality" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should refresh system data" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display health status icons correctly" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display token usage with proper formatting" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should handle loading states properly" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display error details when errors exist" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should handle cache clearing loading state" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should maintain responsive layout" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should handle error state gracefully" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="user-management.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Mobile Safari" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="User Management › should display user management page correctly" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should display users table with correct columns" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should display user data in table rows" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should search users successfully" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should update user role successfully" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should toggle user status successfully" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should refresh user data" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should handle pagination if available" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should display user count information" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should handle empty search results" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should display user icons correctly" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should handle loading states" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Microsoft Edge" tests="8" failures="0" skipped="8" time="0" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin-dashboard.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Microsoft Edge" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Admin Dashboard › should display dashboard overview correctly" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display system health status" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display user statistics" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display collection statistics" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display feedback statistics" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display performance metrics" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should refresh dashboard data" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should navigate to different admin sections" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display sidebar navigation correctly" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should handle mobile navigation" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display error state gracefully" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should maintain active navigation state" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin-integration.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Microsoft Edge" tests="10" failures="0" skipped="10" time="0" errors="0">
<testcase name="Admin Integration Tests › should complete full admin workflow" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should maintain session across navigation" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle concurrent admin operations" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle admin logout and re-login" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle browser refresh on admin pages" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle admin operations with error recovery" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should maintain admin privileges throughout session" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle mobile responsive admin interface" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle admin data consistency" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle admin error states gracefully" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="audit-logs.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Microsoft Edge" tests="18" failures="0" skipped="18" time="0" errors="0">
<testcase name="Audit Logs › should display audit logs page correctly" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display filters section" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display audit events section" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display audit event details" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should filter audit logs by action" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should filter audit logs by resource" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should filter audit logs by user ID" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should clear filters" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display event badges with correct colors" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display event icons correctly" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display event metadata" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should handle pagination if available" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display pagination information" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should refresh audit logs" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should handle empty audit logs state" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display JSON event details" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should handle loading states" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should maintain responsive layout" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="collection-management.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Microsoft Edge" tests="15" failures="0" skipped="15" time="0" errors="0">
<testcase name="Collection Management › should display collection management page correctly" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display collections in grid layout" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display collection cards with correct information" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display language badges correctly" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display content statistics" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display collection metadata" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle collection deletion with confirmation" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle view collection action" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should refresh collections data" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle pagination if available" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display pagination information" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle empty state correctly" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display collection icons correctly" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle loading states" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should maintain responsive layout" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="feedback-management.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Microsoft Edge" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Feedback Management › should display feedback management page correctly" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback list" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback status badges" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle feedback status updates" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback messages" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle feedback filtering" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback timestamps" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle refresh functionality" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display user information for feedback" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle pagination if available" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback count information" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle empty feedback state" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback actions" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle loading states" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should maintain responsive layout" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle feedback search functionality" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="system-monitoring.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Microsoft Edge" tests="14" failures="0" skipped="14" time="0" errors="0">
<testcase name="System Monitoring › should display system monitoring page correctly" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display system health overview" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display system components status" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display AI token usage statistics" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display recent errors section" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should handle cache clearing functionality" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should refresh system data" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display health status icons correctly" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display token usage with proper formatting" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should handle loading states properly" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display error details when errors exist" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should handle cache clearing loading state" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should maintain responsive layout" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should handle error state gracefully" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="user-management.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Microsoft Edge" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="User Management › should display user management page correctly" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should display users table with correct columns" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should display user data in table rows" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should search users successfully" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should update user role successfully" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should toggle user status successfully" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should refresh user data" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should handle pagination if available" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should display user count information" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should handle empty search results" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should display user icons correctly" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should handle loading states" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Google Chrome" tests="8" failures="0" skipped="8" time="0" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin-dashboard.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Google Chrome" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Admin Dashboard › should display dashboard overview correctly" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display system health status" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display user statistics" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display collection statistics" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display feedback statistics" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display performance metrics" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should refresh dashboard data" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should navigate to different admin sections" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display sidebar navigation correctly" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should handle mobile navigation" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display error state gracefully" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should maintain active navigation state" classname="admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin-integration.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Google Chrome" tests="10" failures="0" skipped="10" time="0" errors="0">
<testcase name="Admin Integration Tests › should complete full admin workflow" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should maintain session across navigation" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle concurrent admin operations" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle admin logout and re-login" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle browser refresh on admin pages" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle admin operations with error recovery" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should maintain admin privileges throughout session" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle mobile responsive admin interface" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle admin data consistency" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Integration Tests › should handle admin error states gracefully" classname="admin-integration.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="audit-logs.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Google Chrome" tests="18" failures="0" skipped="18" time="0" errors="0">
<testcase name="Audit Logs › should display audit logs page correctly" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display filters section" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display audit events section" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display audit event details" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should filter audit logs by action" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should filter audit logs by resource" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should filter audit logs by user ID" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should clear filters" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display event badges with correct colors" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display event icons correctly" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display event metadata" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should handle pagination if available" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display pagination information" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should refresh audit logs" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should handle empty audit logs state" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should display JSON event details" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should handle loading states" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Audit Logs › should maintain responsive layout" classname="audit-logs.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="collection-management.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Google Chrome" tests="15" failures="0" skipped="15" time="0" errors="0">
<testcase name="Collection Management › should display collection management page correctly" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display collections in grid layout" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display collection cards with correct information" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display language badges correctly" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display content statistics" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display collection metadata" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle collection deletion with confirmation" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle view collection action" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should refresh collections data" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle pagination if available" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display pagination information" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle empty state correctly" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should display collection icons correctly" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should handle loading states" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Collection Management › should maintain responsive layout" classname="collection-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="feedback-management.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Google Chrome" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Feedback Management › should display feedback management page correctly" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback list" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback status badges" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle feedback status updates" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback messages" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle feedback filtering" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback timestamps" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle refresh functionality" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display user information for feedback" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle pagination if available" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback count information" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle empty feedback state" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should display feedback actions" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle loading states" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should maintain responsive layout" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Feedback Management › should handle feedback search functionality" classname="feedback-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="system-monitoring.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Google Chrome" tests="14" failures="0" skipped="14" time="0" errors="0">
<testcase name="System Monitoring › should display system monitoring page correctly" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display system health overview" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display system components status" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display AI token usage statistics" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display recent errors section" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should handle cache clearing functionality" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should refresh system data" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display health status icons correctly" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display token usage with proper formatting" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should handle loading states properly" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should display error details when errors exist" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should handle cache clearing loading state" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should maintain responsive layout" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="System Monitoring › should handle error state gracefully" classname="system-monitoring.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="user-management.spec.ts" timestamp="2025-07-21T15:06:43.696Z" hostname="Google Chrome" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="User Management › should display user management page correctly" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should display users table with correct columns" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should display user data in table rows" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should search users successfully" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should update user role successfully" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should toggle user status successfully" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should refresh user data" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should handle pagination if available" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should display user count information" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should handle empty search results" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should display user icons correctly" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Management › should handle loading states" classname="user-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
</testsuites>