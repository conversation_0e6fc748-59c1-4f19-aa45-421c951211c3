<testsuites id="" name="" tests="1" failures="1" skipped="0" errors="0" time="13.237238">
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T14:34:21.934Z" hostname="chromium" tests="1" failures="1" skipped="0" time="1.219" errors="0">
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="1.219">
<failure message="admin-auth.spec.ts:25:6 should login successfully with valid admin credentials" type="FAILURE">
<![CDATA[  [chromium] › admin-auth.spec.ts:25:6 › Admin Authentication › should login successfully with valid admin credentials 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      46 |
      47 | 		// Should show either content or error state
    > 48 | 		expect(hasContent || hasError || hasTryAgain).toBe(true);
         | 		                                              ^
      49 |
      50 | 		// Check navigation sidebar
      51 | 		await expect(page.locator('text=Admin Panel')).toBeVisible();
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:48:49

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-chromium/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>