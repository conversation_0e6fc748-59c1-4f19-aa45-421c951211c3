<testsuites id="" name="" tests="56" failures="14" skipped="0" errors="0" time="171.868874">
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T15:50:32.758Z" hostname="chromium" tests="8" failures="1" skipped="0" time="32.596" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="1.737">
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="5.575">
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="2.449">
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="11.858">
<failure message="admin-auth.spec.ts:78:6 should show validation errors for empty fields" type="FAILURE">
<![CDATA[  [chromium] › admin-auth.spec.ts:78:6 › Admin Authentication › should show validation errors for empty fields 

    Error: Timed out 10000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })


      85 | 				.locator('[data-sonner-toast]')
      86 | 				.filter({ hasText: 'Please enter both username and password' })
    > 87 | 		).toBeVisible({ timeout: 10000 });
         | 		  ^
      88 | 	});
      89 |
      90 | 	test('should toggle password visibility', async ({ page }) => {
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:87:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-chromium/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="1.541">
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="4.674">
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="2.608">
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="2.154">
</testcase>
</testsuite>
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T15:50:32.758Z" hostname="firefox" tests="8" failures="2" skipped="0" time="108.091" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="3.729">
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="20.626">
<failure message="admin-auth.spec.ts:25:6 should login successfully with valid admin credentials" type="FAILURE">
<![CDATA[  [firefox] › admin-auth.spec.ts:25:6 › Admin Authentication › should login successfully with valid admin credentials 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      52 |
      53 | 		// Should show either content or error state
    > 54 | 		expect(hasContent || hasError || hasTryAgain).toBe(true);
         | 		                                              ^
      55 |
      56 | 		// Check navigation sidebar
      57 | 		await expect(page.locator('text=Admin Panel')).toBeVisible();
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:54:49

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-firefox/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="7.966">
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="28.103">
<failure message="admin-auth.spec.ts:78:6 should show validation errors for empty fields" type="FAILURE">
<![CDATA[  [firefox] › admin-auth.spec.ts:78:6 › Admin Authentication › should show validation errors for empty fields 

    Error: Timed out 10000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })


      85 | 				.locator('[data-sonner-toast]')
      86 | 				.filter({ hasText: 'Please enter both username and password' })
    > 87 | 		).toBeVisible({ timeout: 10000 });
         | 		  ^
      88 | 	});
      89 |
      90 | 	test('should toggle password visibility', async ({ page }) => {
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:87:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-firefox/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="5.475">
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="16.561">
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="14.884">
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="10.747">
</testcase>
</testsuite>
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T15:50:32.758Z" hostname="webkit" tests="8" failures="3" skipped="0" time="124.376" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="11.946">
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="13.222">
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="10.16">
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="15.948">
<failure message="admin-auth.spec.ts:78:6 should show validation errors for empty fields" type="FAILURE">
<![CDATA[  [webkit] › admin-auth.spec.ts:78:6 › Admin Authentication › should show validation errors for empty fields 

    Error: Timed out 10000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })


      85 | 				.locator('[data-sonner-toast]')
      86 | 				.filter({ hasText: 'Please enter both username and password' })
    > 87 | 		).toBeVisible({ timeout: 10000 });
         | 		  ^
      88 | 	});
      89 |
      90 | 	test('should toggle password visibility', async ({ page }) => {
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:87:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-webkit/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-webkit/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-webkit/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="1.287">
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="31.886">
<failure message="admin-auth.spec.ts:110:6 should logout successfully" type="FAILURE">
<![CDATA[  [webkit] › admin-auth.spec.ts:110:6 › Admin Authentication › should logout successfully ──────────

    Test timeout of 30000ms exceeded.

    Error: page.waitForURL: Test timeout of 30000ms exceeded.
    =========================== logs ===========================
    waiting for navigation to "/admin" until "load"
    ============================================================

      113 | 		await page.fill('input[id="password"]', 'admin123');
      114 | 		await page.click('button[type="submit"]');
    > 115 | 		await page.waitForURL('/admin');
          | 		           ^
      116 |
      117 | 		// Find and click logout button
      118 | 		const logoutButton = page.locator('button').filter({ hasText: 'Logout' });
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:115:14

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-webkit/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-webkit/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-webkit/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="9.534">
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="30.393">
<failure message="admin-auth.spec.ts:137:6 should handle loading states during login" type="FAILURE">
<![CDATA[  [webkit] › admin-auth.spec.ts:137:6 › Admin Authentication › should handle loading states during login 

    Test timeout of 30000ms exceeded.

    Error: page.waitForURL: Test timeout of 30000ms exceeded.
    =========================== logs ===========================
    waiting for navigation to "/admin" until "load"
    ============================================================

      149 |
      150 | 		// Wait for successful navigation
    > 151 | 		await page.waitForURL('/admin');
          | 		           ^
      152 | 	});
      153 | });
      154 |
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:151:14

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-1b10f-loading-states-during-login-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-1b10f-loading-states-during-login-webkit/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentic-1b10f-loading-states-during-login-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-1b10f-loading-states-during-login-webkit/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-1b10f-loading-states-during-login-webkit/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-1b10f-loading-states-during-login-webkit/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T15:50:32.758Z" hostname="Mobile Chrome" tests="8" failures="2" skipped="0" time="45.529" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="2.252">
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="5.633">
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="3.247">
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="12.297">
<failure message="admin-auth.spec.ts:78:6 should show validation errors for empty fields" type="FAILURE">
<![CDATA[  [Mobile Chrome] › admin-auth.spec.ts:78:6 › Admin Authentication › should show validation errors for empty fields 

    Error: Timed out 10000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })


      85 | 				.locator('[data-sonner-toast]')
      86 | 				.filter({ hasText: 'Please enter both username and password' })
    > 87 | 		).toBeVisible({ timeout: 10000 });
         | 		  ^
      88 | 	});
      89 |
      90 | 	test('should toggle password visibility', async ({ page }) => {
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:87:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Mobile-Chrome/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Mobile-Chrome/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="1.857">
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="14.67">
<failure message="admin-auth.spec.ts:110:6 should logout successfully" type="FAILURE">
<![CDATA[  [Mobile Chrome] › admin-auth.spec.ts:110:6 › Admin Authentication › should logout successfully ───

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('button').filter({ hasText: 'Logout' })
        - locator resolved to <button id="_r_8_" tabindex="0" role="button" data-slot="button" aria-disabled="false" class="inline-flex items-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:bo…>…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
        - retrying click action
        - waiting 20ms
        2 × waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
        - retrying click action
          - waiting 100ms
        18 × waiting for element to be visible, enabled and stable
           - element is visible, enabled and stable
           - scrolling into view if needed
           - done scrolling
           - element is outside of the viewport
         - retrying click action
           - waiting 500ms


      117 | 		// Find and click logout button
      118 | 		const logoutButton = page.locator('button').filter({ hasText: 'Logout' });
    > 119 | 		await logoutButton.click();
          | 		                   ^
      120 |
      121 | 		// Should redirect to login page
      122 | 		await page.waitForURL('/admin/login');
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:119:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Chrome/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Chrome/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="2.133">
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="3.44">
</testcase>
</testsuite>
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T15:50:32.758Z" hostname="Mobile Safari" tests="8" failures="3" skipped="0" time="62.487" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="1.623">
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="6.137">
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="11.76">
<failure message="admin-auth.spec.ts:61:6 should show error for invalid credentials" type="FAILURE">
<![CDATA[  [Mobile Safari] › admin-auth.spec.ts:61:6 › Admin Authentication › should show error for invalid credentials 

    Error: Timed out 10000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-sonner-toast]').filter({ hasText: 'Invalid username or password' })
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Invalid username or password' })


      70 | 		await expect(
      71 | 			page.locator('[data-sonner-toast]').filter({ hasText: 'Invalid username or password' })
    > 72 | 		).toBeVisible({ timeout: 10000 });
         | 		  ^
      73 |
      74 | 		// Should still be on login page
      75 | 		await expect(page).toHaveURL('/admin/login');
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:72:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-224fa-ror-for-invalid-credentials-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-224fa-ror-for-invalid-credentials-Mobile-Safari/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentic-224fa-ror-for-invalid-credentials-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-224fa-ror-for-invalid-credentials-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-224fa-ror-for-invalid-credentials-Mobile-Safari/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-224fa-ror-for-invalid-credentials-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="12.326">
<failure message="admin-auth.spec.ts:78:6 should show validation errors for empty fields" type="FAILURE">
<![CDATA[  [Mobile Safari] › admin-auth.spec.ts:78:6 › Admin Authentication › should show validation errors for empty fields 

    Error: Timed out 10000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })


      85 | 				.locator('[data-sonner-toast]')
      86 | 				.filter({ hasText: 'Please enter both username and password' })
    > 87 | 		).toBeVisible({ timeout: 10000 });
         | 		  ^
      88 | 	});
      89 |
      90 | 	test('should toggle password visibility', async ({ page }) => {
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:87:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Mobile-Safari/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Mobile-Safari/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="3.007">
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="17.809">
<failure message="admin-auth.spec.ts:110:6 should logout successfully" type="FAILURE">
<![CDATA[  [Mobile Safari] › admin-auth.spec.ts:110:6 › Admin Authentication › should logout successfully ───

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('button').filter({ hasText: 'Logout' })
        - locator resolved to <button id="_r_a_" tabindex="0" role="button" data-slot="button" aria-disabled="false" class="inline-flex items-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:bo…>…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
        - retrying click action
        - waiting 20ms
        2 × waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - element is outside of the viewport
        - retrying click action
          - waiting 100ms
        17 × waiting for element to be visible, enabled and stable
           - element is visible, enabled and stable
           - scrolling into view if needed
           - done scrolling
           - element is outside of the viewport
         - retrying click action
           - waiting 500ms


      117 | 		// Find and click logout button
      118 | 		const logoutButton = page.locator('button').filter({ hasText: 'Logout' });
    > 119 | 		await logoutButton.click();
          | 		                   ^
      120 |
      121 | 		// Should redirect to login page
      122 | 		await page.waitForURL('/admin/login');
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:119:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Safari/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Safari/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="6.261">
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="3.564">
</testcase>
</testsuite>
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T15:50:32.758Z" hostname="Microsoft Edge" tests="8" failures="2" skipped="0" time="68.478" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="4.368">
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="15.531">
<failure message="admin-auth.spec.ts:25:6 should login successfully with valid admin credentials" type="FAILURE">
<![CDATA[  [Microsoft Edge] › admin-auth.spec.ts:25:6 › Admin Authentication › should login successfully with valid admin credentials 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      52 |
      53 | 		// Should show either content or error state
    > 54 | 		expect(hasContent || hasError || hasTryAgain).toBe(true);
         | 		                                              ^
      55 |
      56 | 		// Check navigation sidebar
      57 | 		await expect(page.locator('text=Admin Panel')).toBeVisible();
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:54:49

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-Microsoft-Edge/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-Microsoft-Edge/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-Microsoft-Edge/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-Microsoft-Edge/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-Microsoft-Edge/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-Microsoft-Edge/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="12.305">
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="17.153">
<failure message="admin-auth.spec.ts:78:6 should show validation errors for empty fields" type="FAILURE">
<![CDATA[  [Microsoft Edge] › admin-auth.spec.ts:78:6 › Admin Authentication › should show validation errors for empty fields 

    Error: Timed out 10000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })


      85 | 				.locator('[data-sonner-toast]')
      86 | 				.filter({ hasText: 'Please enter both username and password' })
    > 87 | 		).toBeVisible({ timeout: 10000 });
         | 		  ^
      88 | 	});
      89 |
      90 | 	test('should toggle password visibility', async ({ page }) => {
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:87:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Microsoft-Edge/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Microsoft-Edge/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Microsoft-Edge/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Microsoft-Edge/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Microsoft-Edge/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Microsoft-Edge/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="7.015">
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="5.572">
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="3.001">
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="3.533">
</testcase>
</testsuite>
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T15:50:32.758Z" hostname="Google Chrome" tests="8" failures="1" skipped="0" time="32.159" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="4.095">
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="6.203">
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="1.394">
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="10.778">
<failure message="admin-auth.spec.ts:78:6 should show validation errors for empty fields" type="FAILURE">
<![CDATA[  [Google Chrome] › admin-auth.spec.ts:78:6 › Admin Authentication › should show validation errors for empty fields 

    Error: Timed out 10000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })


      85 | 				.locator('[data-sonner-toast]')
      86 | 				.filter({ hasText: 'Please enter both username and password' })
    > 87 | 		).toBeVisible({ timeout: 10000 });
         | 		  ^
      88 | 	});
      89 |
      90 | 	test('should toggle password visibility', async ({ page }) => {
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:87:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Google-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Google-Chrome/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Google-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Google-Chrome/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Google-Chrome/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Google-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="1.104">
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="3.802">
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="3.258">
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="1.525">
</testcase>
</testsuite>
</testsuites>