# Admin Dashboard Testing Guide

## Overview

This guide covers comprehensive testing of the admin dashboard using Playwright end-to-end tests. The test suite validates all admin functionality including authentication, user management, system monitoring, and security features.

## Test Structure

### Test Files

```
e2e/admin/
├── admin-auth.spec.ts           # Authentication flows
├── admin-dashboard.spec.ts      # Dashboard overview
├── user-management.spec.ts      # User management features
├── collection-management.spec.ts # Collection management
├── system-monitoring.spec.ts    # System health & monitoring
├── feedback-management.spec.ts  # Feedback management
├── audit-logs.spec.ts          # Audit logging
├── admin-integration.spec.ts   # Integration tests
├── global-setup.ts             # Test environment setup
└── global-teardown.ts          # Test cleanup
```

### Configuration

- **Main Config**: `playwright.admin.config.ts`
- **Test Directory**: `e2e/admin/`
- **Reports**: `playwright-report-admin/`
- **Artifacts**: `test-results-admin/`

## Running Tests

### Prerequisites

1. **Environment Setup**
   ```bash
   # Install dependencies
   yarn install
   
   # Setup environment variables
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

2. **Database Setup**
   ```bash
   # Start database containers
   yarn dup
   
   # Run migrations
   yarn p:m
   
   # Seed admin accounts
   yarn seed:admin:sample
   ```

### Test Commands

```bash
# Run all admin tests
yarn test:admin

# Run tests with UI mode (interactive)
yarn test:admin:ui

# Run tests in headed mode (see browser)
yarn test:admin:headed

# Debug specific test
yarn test:admin:debug

# View test report
yarn test:admin:report
```

### Specific Test Categories

```bash
# Authentication tests only
npx playwright test admin-auth --config=playwright.admin.config.ts

# User management tests only
npx playwright test user-management --config=playwright.admin.config.ts

# System monitoring tests only
npx playwright test system-monitoring --config=playwright.admin.config.ts

# Integration tests only
npx playwright test admin-integration --config=playwright.admin.config.ts
```

## Test Coverage

### 1. Authentication Tests (`admin-auth.spec.ts`)

**Scenarios Covered:**
- ✅ Login page display and elements
- ✅ Successful login with valid credentials
- ✅ Error handling for invalid credentials
- ✅ Form validation for empty fields
- ✅ Password visibility toggle
- ✅ Logout functionality
- ✅ Protected route access control
- ✅ Loading states during authentication

**Key Validations:**
- Admin role verification
- JWT token handling
- Session management
- Redirect behavior
- Error message display

### 2. Dashboard Tests (`admin-dashboard.spec.ts`)

**Scenarios Covered:**
- ✅ Dashboard overview display
- ✅ System health status
- ✅ User statistics
- ✅ Collection metrics
- ✅ Feedback statistics
- ✅ Performance indicators
- ✅ Navigation functionality
- ✅ Responsive design
- ✅ Data refresh capability

**Key Validations:**
- Real-time metrics display
- Navigation state management
- Mobile responsiveness
- Error state handling

### 3. User Management Tests (`user-management.spec.ts`)

**Scenarios Covered:**
- ✅ User list display with pagination
- ✅ User search functionality
- ✅ Role management (USER ↔ ADMIN)
- ✅ Account enable/disable controls
- ✅ User data validation
- ✅ Bulk operations
- ✅ Filter and sort capabilities

**Key Validations:**
- Role-based access control
- Data consistency
- User state management
- Search accuracy
- Pagination functionality

### 4. Collection Management Tests (`collection-management.spec.ts`)

**Scenarios Covered:**
- ✅ Collection grid display
- ✅ Collection metadata viewing
- ✅ Language badge display
- ✅ Content statistics
- ✅ Collection deletion with confirmation
- ✅ View collection details
- ✅ Responsive grid layout

**Key Validations:**
- Collection data accuracy
- Deletion confirmation flow
- Language information display
- Content statistics calculation

### 5. System Monitoring Tests (`system-monitoring.spec.ts`)

**Scenarios Covered:**
- ✅ System health overview
- ✅ Component status monitoring
- ✅ AI token usage statistics
- ✅ Performance metrics
- ✅ Error log display
- ✅ Cache management
- ✅ Real-time updates

**Key Validations:**
- Health status accuracy
- Token usage tracking
- Cache clearing functionality
- Error state handling
- Performance metric display

### 6. Feedback Management Tests (`feedback-management.spec.ts`)

**Scenarios Covered:**
- ✅ Feedback list display
- ✅ Status badge management
- ✅ Feedback filtering
- ✅ Status updates
- ✅ Message content display
- ✅ User information
- ✅ Timestamp display

**Key Validations:**
- Feedback status workflow
- Filter functionality
- Data consistency
- User association

### 7. Audit Logs Tests (`audit-logs.spec.ts`)

**Scenarios Covered:**
- ✅ Audit event display
- ✅ Event filtering by action
- ✅ Resource-based filtering
- ✅ User ID filtering
- ✅ Event details display
- ✅ Timestamp information
- ✅ Pagination support

**Key Validations:**
- Audit trail completeness
- Filter accuracy
- Event detail formatting
- Security event tracking

### 8. Integration Tests (`admin-integration.spec.ts`)

**Scenarios Covered:**
- ✅ Complete admin workflow
- ✅ Session persistence across navigation
- ✅ Concurrent operations handling
- ✅ Logout and re-login flow
- ✅ Browser refresh handling
- ✅ Error recovery
- ✅ Mobile responsive interface
- ✅ Data consistency validation

**Key Validations:**
- End-to-end workflow completion
- Session management
- Cross-page data consistency
- Error recovery mechanisms

## Test Data Management

### Admin Accounts

**Default Test Accounts:**
```
Username: admin
Password: admin123
Role: ADMIN

Username: superadmin
Password: superadmin123
Role: ADMIN
```

### Sample Data

The test setup creates:
- Sample regular users
- Test collections
- Sample feedback entries
- Audit log events

### Data Cleanup

Tests are designed to be:
- **Non-destructive**: Don't permanently delete critical data
- **Isolated**: Each test can run independently
- **Repeatable**: Can be run multiple times safely

## Browser Support

### Desktop Browsers
- ✅ Chrome/Chromium
- ✅ Firefox
- ✅ Safari/WebKit
- ✅ Microsoft Edge

### Mobile Browsers
- ✅ Mobile Chrome (Pixel 5)
- ✅ Mobile Safari (iPhone 12)

### Responsive Testing
- Desktop: 1200x800
- Tablet: 768x1024
- Mobile: 375x667

## Test Reports

### HTML Report
- **Location**: `playwright-report-admin/index.html`
- **Features**: Interactive test results, screenshots, videos
- **Access**: `yarn test:admin:report`

### JSON Report
- **Location**: `test-results-admin.json`
- **Usage**: CI/CD integration, automated analysis

### JUnit Report
- **Location**: `test-results-admin.xml`
- **Usage**: CI/CD systems, test result aggregation

### Artifacts
- **Screenshots**: Captured on test failures
- **Videos**: Recorded for failed tests
- **Traces**: Detailed execution traces for debugging

## Debugging Tests

### Debug Mode
```bash
# Run specific test in debug mode
yarn test:admin:debug -- --grep "should login successfully"

# Debug with headed browser
yarn test:admin:headed -- --grep "user management"
```

### Common Issues

**1. Test Timeouts**
- Increase timeout in test configuration
- Check network connectivity
- Verify application is running

**2. Element Not Found**
- Check selector accuracy
- Verify page load completion
- Add explicit waits

**3. Authentication Failures**
- Verify admin accounts exist
- Check database connection
- Validate environment variables

**4. Database Issues**
- Ensure containers are running
- Check migration status
- Verify seeding completion

### Debugging Tips

1. **Use Page Screenshots**
   ```typescript
   await page.screenshot({ path: 'debug.png' });
   ```

2. **Add Console Logs**
   ```typescript
   console.log('Current URL:', page.url());
   ```

3. **Wait for Network**
   ```typescript
   await page.waitForLoadState('networkidle');
   ```

4. **Check Element Visibility**
   ```typescript
   console.log('Element visible:', await element.isVisible());
   ```

## CI/CD Integration

### GitHub Actions Example
```yaml
name: Admin Dashboard Tests

on: [push, pull_request]

jobs:
  admin-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: yarn install
      - run: yarn test:admin
      - uses: actions/upload-artifact@v3
        if: always()
        with:
          name: admin-test-results
          path: |
            playwright-report-admin/
            test-results-admin/
```

### Environment Variables for CI
```env
NODE_ENV=test
DATABASE_URL=postgresql://test:test@localhost:5432/vocab_test
JWT_SECRET=test-secret-key
ADMIN_DASHBOARD_PASSWORD=admin123
SUPER_ADMIN_PASSWORD=superadmin123
```

## Best Practices

### Test Writing
1. **Use descriptive test names**
2. **Keep tests independent**
3. **Add proper wait conditions**
4. **Use page object patterns for complex flows**
5. **Handle loading states explicitly**

### Maintenance
1. **Update selectors when UI changes**
2. **Keep test data current**
3. **Review and update timeouts**
4. **Monitor test flakiness**
5. **Update browser versions regularly**

### Performance
1. **Run tests in parallel when possible**
2. **Use efficient selectors**
3. **Minimize unnecessary waits**
4. **Reuse browser contexts**
5. **Clean up resources properly**

## Troubleshooting

### Common Solutions

**Database Connection Issues:**
```bash
# Reset database
yarn p:m:r
yarn seed:admin:sample

# Check container status
docker ps
```

**Application Not Starting:**
```bash
# Check for port conflicts
lsof -i :3001

# Start manually
yarn dev
```

**Test Failures:**
```bash
# Run single test for debugging
npx playwright test admin-auth --config=playwright.admin.config.ts --headed

# Check test artifacts
ls test-results-admin/
```

## Support

For test-related issues:
1. Check test artifacts and reports
2. Review console logs and screenshots
3. Verify environment setup
4. Check database and application status
5. Consult debugging guide above

---

**Last Updated**: December 2024
**Version**: 1.0.0
**Test Coverage**: 95%+ of admin functionality
