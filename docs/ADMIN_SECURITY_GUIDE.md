# Admin System Security Guide

## Overview

This guide covers security best practices, configurations, and procedures for the Vocab Admin System.

## Authentication Security

### JWT Configuration

**Secure JWT Settings:**
```env
# Use a strong, random secret (minimum 32 characters)
JWT_SECRET=your-super-secure-random-secret-key-here-minimum-32-chars

# Cookie configuration
JWT_COOKIE_NAME=auth_token
JWT_EXPIRES_IN=2592000  # 30 days

# Production settings
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=another-strong-random-secret
```

**JWT Security Features:**
- HttpOnly cookies prevent XSS attacks
- Secure flag for HTTPS-only transmission
- SameSite=strict prevents CSRF attacks
- Automatic token expiration
- Server-side token validation

### Password Security

**Default Password Policy:**
- Minimum 6 characters (increase for production)
- Bcrypt hashing with salt rounds
- No password reuse validation (implement if needed)

**Production Recommendations:**
- Minimum 12 characters
- Require uppercase, lowercase, numbers, symbols
- Implement password history
- Force password changes on first login
- Enable account lockout after failed attempts

### Role-Based Access Control

**Role Hierarchy:**
```typescript
enum Role {
  USER = 'USER',      // Regular users
  ADMIN = 'ADMIN'     // Full admin access
}
```

**Permission Matrix:**
- **USER**: Access to own data only
- **ADMIN**: Full system access, user management, system monitoring

## API Security

### Middleware Protection

**Admin Routes Protection:**
```typescript
// All /api/admin/* routes require admin authentication
export const adminMiddleware = async (request: NextRequest) => {
  // 1. Verify JWT token
  // 2. Check user exists and is active
  // 3. Verify admin role
  // 4. Add user context to request
}
```

**Protected Endpoints:**
- `/api/admin/dashboard` - Dashboard data
- `/api/admin/users` - User management
- `/api/admin/collections` - Collection management
- `/api/admin/system` - System monitoring
- `/api/admin/audit` - Audit logs
- `/api/admin/feedback` - Feedback management

**Public Endpoints:**
- `/api/admin/auth` - Login/logout (no auth required)

### Input Validation

**Zod Schema Validation:**
```typescript
// All API inputs are validated using Zod schemas
const updateUserSchema = z.object({
  userId: z.string().uuid(),
  role: z.nativeEnum(Role).optional(),
  disabled: z.boolean().optional(),
});
```

**Validation Features:**
- Type safety with TypeScript
- Runtime validation
- Sanitization of inputs
- Error handling with detailed messages

### Rate Limiting

**Current Implementation:**
- Basic rate limiting in middleware
- IP-based request throttling

**Production Recommendations:**
- Implement Redis-based rate limiting
- Different limits for different endpoints
- User-based rate limiting
- Exponential backoff for failed attempts

## Data Security

### Database Security

**Connection Security:**
- SSL/TLS encrypted connections
- Connection pooling with limits
- Prepared statements (Prisma ORM)
- No direct SQL injection vectors

**Data Protection:**
- Password hashing with bcrypt
- Sensitive data exclusion in API responses
- Audit trail for all data changes

### Audit Logging

**Security Events Tracked:**
```typescript
export const AUDIT_ACTIONS = {
  // Authentication
  ADMIN_LOGIN: 'admin_login',
  ADMIN_LOGOUT: 'admin_logout',
  
  // User Management
  ADMIN_USER_CREATED: 'admin_user_created',
  ADMIN_USER_UPDATED: 'admin_user_updated',
  ADMIN_USER_DISABLED: 'admin_user_disabled',
  
  // System Events
  ADMIN_CACHE_CLEARED: 'admin_cache_cleared',
  SYSTEM_ERROR: 'system_error',
};
```

**Audit Data Includes:**
- Timestamp
- User/Admin ID
- Action performed
- Resource affected
- IP address
- User agent
- Additional context

## Network Security

### HTTPS Configuration

**Production Requirements:**
- Force HTTPS for all connections
- HSTS headers
- Secure cookie flags
- Certificate validation

**Next.js Configuration:**
```typescript
// next.config.js
module.exports = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=31536000; includeSubDomains'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          }
        ]
      }
    ];
  }
};
```

### CORS Configuration

**Current Settings:**
- Configurable origin validation
- Credentials support for cookies
- Method restrictions

**Production Recommendations:**
- Whitelist specific domains
- Disable credentials for public APIs
- Implement preflight caching

## Monitoring & Alerting

### Security Monitoring

**Events to Monitor:**
- Failed login attempts
- Privilege escalation attempts
- Unusual admin activity
- System errors and warnings
- Cache manipulation
- Data deletion events

**Alert Triggers:**
- Multiple failed logins from same IP
- Admin role changes
- System health degradation
- Unusual API usage patterns

### Audit Log Analysis

**Regular Reviews:**
- Weekly admin activity review
- Monthly security audit
- Quarterly access review
- Annual security assessment

**Key Metrics:**
- Admin login frequency
- Failed authentication attempts
- Data modification patterns
- System error rates

## Incident Response

### Security Incident Procedures

**1. Detection:**
- Monitor audit logs
- Check system alerts
- User reports

**2. Assessment:**
- Determine scope and impact
- Identify affected systems
- Assess data exposure

**3. Containment:**
- Disable compromised accounts
- Revoke authentication tokens
- Block suspicious IP addresses
- Isolate affected systems

**4. Recovery:**
- Restore from backups if needed
- Reset compromised passwords
- Update security configurations
- Apply security patches

**5. Documentation:**
- Record incident details
- Document response actions
- Update security procedures
- Conduct post-incident review

### Emergency Procedures

**Admin Account Lockout:**
```bash
# Connect to database and disable admin account
psql $DATABASE_URL -c "UPDATE \"User\" SET disabled = true WHERE id = 'admin-user-id';"
```

**Token Revocation:**
```bash
# Change JWT secret to invalidate all tokens
# Update JWT_SECRET in environment variables
# Restart application
```

**System Lockdown:**
```bash
# Disable all admin access temporarily
# Set maintenance mode
# Block admin routes at load balancer
```

## Compliance & Best Practices

### Security Standards

**Follow Industry Standards:**
- OWASP Top 10 compliance
- NIST Cybersecurity Framework
- ISO 27001 principles
- SOC 2 Type II controls

### Regular Security Tasks

**Daily:**
- Monitor audit logs
- Check system health
- Review failed login attempts

**Weekly:**
- Admin activity review
- Security alert analysis
- System update checks

**Monthly:**
- Access review and cleanup
- Security configuration audit
- Vulnerability assessment

**Quarterly:**
- Penetration testing
- Security training updates
- Incident response drills

### Data Privacy

**Personal Data Protection:**
- Minimal data collection
- Purpose limitation
- Data retention policies
- User consent management
- Right to deletion

**Admin Data Handling:**
- Principle of least privilege
- Need-to-know access
- Data classification
- Secure data disposal

## Security Checklist

### Pre-Production
- [ ] Change all default passwords
- [ ] Configure strong JWT secrets
- [ ] Enable HTTPS with valid certificates
- [ ] Set up security headers
- [ ] Configure CORS policies
- [ ] Enable audit logging
- [ ] Set up monitoring and alerting
- [ ] Conduct security testing
- [ ] Review access controls
- [ ] Document security procedures

### Post-Production
- [ ] Monitor security events
- [ ] Regular security reviews
- [ ] Keep systems updated
- [ ] Backup audit logs
- [ ] Test incident response
- [ ] Train admin users
- [ ] Review and update policies
- [ ] Conduct security assessments

## Contact Information

**Security Team:**
- Email: <EMAIL>
- Emergency: +1-XXX-XXX-XXXX
- Incident Response: <EMAIL>

**Escalation:**
1. System Administrator
2. Security Team Lead
3. CTO/CISO
4. Legal/Compliance Team

---

**Last Updated**: December 2024
**Version**: 1.0.0
**Classification**: Internal Use Only
