# Admin System Setup Guide

## Overview

The Vocab Admin System provides comprehensive administrative capabilities for managing users, collections, system monitoring, and security auditing. This guide covers setup, configuration, and usage of the admin dashboard.

## Features

### 🔐 Authentication & Security
- Role-based access control (USER/ADMIN)
- Secure JWT-based authentication
- Admin-only middleware protection
- Session management with secure cookies

### 👥 User Management
- View all users with pagination and search
- Update user roles (USER ↔ ADMIN)
- Enable/disable user accounts
- User activity tracking

### 📁 Collection Management
- View all collections across users
- Delete collections with confirmation
- Collection statistics and metadata
- Language and content analysis

### 📊 System Monitoring
- Real-time system health status
- AI token usage statistics
- Performance metrics
- Cache management

### 📋 Audit Logging
- Comprehensive activity tracking
- Security audit trail
- Filterable event logs
- User and admin action history

### 💬 Feedback Management
- User feedback review and management
- Status tracking (pending/reviewed/resolved)
- Enhanced filtering and search

## Quick Start

### 1. Database Setup

Ensure your database is running and migrations are applied:

```bash
# Start database containers
yarn dup

# Apply database migrations (includes Role enum and admin fields)
yarn p:m
```

### 2. Create Admin Accounts

Run the seeding script to create default admin accounts:

```bash
# Create admin accounts only
yarn seed:admin

# Create admin accounts + sample data for testing
yarn seed:admin:sample
```

**Default Admin Credentials:**
- Username: `admin` | Password: `admin123`
- Username: `superadmin` | Password: `superadmin123`

⚠️ **IMPORTANT**: Change these passwords immediately in production!

### 3. Environment Configuration

Ensure these environment variables are set in your `.env.local`:

```env
# Admin Dashboard Password (used by seeding script)
ADMIN_DASHBOARD_PASSWORD=admin123
SUPER_ADMIN_PASSWORD=superadmin123

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_COOKIE_NAME=auth_token
JWT_EXPIRES_IN=2592000

# Database
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/vocab?schema=public"
```

### 4. Start Development Server

```bash
yarn dev
```

### 5. Access Admin Dashboard

Navigate to: `http://localhost:3000/admin/login`

Use the default credentials to log in and access the admin dashboard.

## Admin Dashboard Structure

### Main Navigation

- **Dashboard** (`/admin`) - Overview and key metrics
- **Users** (`/admin/users`) - User management interface
- **Collections** (`/admin/collections`) - Collection management
- **Feedback** (`/admin/feedback`) - User feedback management
- **System** (`/admin/system`) - System monitoring and health
- **Audit Logs** (`/admin/audit`) - Security audit trail

### Dashboard Overview

The main dashboard provides:
- System health status
- User statistics (total, active, new)
- Collection metrics
- Feedback summary
- Performance indicators

## User Management

### Features
- **View Users**: Paginated list with search functionality
- **Role Management**: Change user roles between USER and ADMIN
- **Account Control**: Enable/disable user accounts
- **User Details**: View user information and metadata

### Actions
- **Update Role**: Select new role from dropdown
- **Toggle Status**: Enable/disable user accounts
- **Search**: Filter users by username or ID

## Collection Management

### Features
- **View Collections**: Grid view with collection details
- **Collection Stats**: Word count, paragraph count, keywords
- **Language Info**: Source and target languages
- **Metadata**: Creation date, user ownership

### Actions
- **Delete Collection**: Remove collections with confirmation
- **View Details**: Inspect collection contents (coming soon)

## System Monitoring

### Health Monitoring
- **Database Status**: Connection and performance
- **Cache Status**: Redis/memory cache health
- **AI Services**: OpenAI API connectivity

### Performance Metrics
- **API Response Time**: Average response times
- **Cache Hit Rate**: Caching efficiency
- **Error Rate**: System error tracking

### Token Usage
- **Total Tokens**: AI API token consumption
- **Cost Tracking**: Estimated costs
- **Request Count**: API call statistics
- **Optimization**: Token usage efficiency

### Cache Management
- **Clear Cache**: Manual cache clearing
- **Cache Statistics**: Hit rates and performance

## Audit Logging

### Event Tracking
The system automatically logs:
- User login/logout events
- Admin actions (user management, deletions)
- System events (errors, cache clears)
- Collection operations
- Feedback management

### Filtering Options
- **Action Type**: Filter by specific actions
- **Resource Type**: Filter by resource (user, collection, etc.)
- **User ID**: Filter by specific user
- **Date Range**: Time-based filtering

### Event Details
Each audit event includes:
- Timestamp
- Action performed
- Resource affected
- User/Admin who performed action
- IP address and user agent
- Additional context details

## Security Considerations

### Authentication
- JWT tokens stored in secure HttpOnly cookies
- Automatic token expiration
- Role-based access control
- Admin-only route protection

### Password Security
- Bcrypt password hashing
- Strong password requirements recommended
- Default password warnings in UI

### Audit Trail
- All admin actions are logged
- Immutable audit records
- Security event tracking
- Failed login attempt monitoring

## API Endpoints

### Authentication
- `POST /api/admin/auth` - Admin login
- `DELETE /api/admin/auth` - Admin logout

### User Management
- `GET /api/admin/users` - List users with pagination
- `POST /api/admin/users` - Create admin user
- `PATCH /api/admin/users` - Update user role/status
- `GET /api/admin/users/[id]` - Get user details

### Collection Management
- `GET /api/admin/collections` - List collections
- `DELETE /api/admin/collections` - Delete collection

### System Monitoring
- `GET /api/admin/dashboard` - Dashboard statistics
- `GET /api/admin/system` - System health and metrics
- `POST /api/admin/system` - Clear cache

### Audit Logging
- `GET /api/admin/audit` - Audit logs with filtering

### Feedback Management
- `GET /api/admin/feedback` - List feedback
- `PATCH /api/admin/feedback` - Update feedback status

## Troubleshooting

### Common Issues

**1. Cannot access admin dashboard**
- Verify admin role is set correctly
- Check JWT token validity
- Ensure admin middleware is working

**2. Database connection errors**
- Verify database is running (`yarn dup`)
- Check DATABASE_URL environment variable
- Run migrations (`yarn p:m`)

**3. Admin login fails**
- Verify admin accounts exist (`yarn seed:admin`)
- Check password correctness
- Review JWT configuration

**4. Missing admin features**
- Ensure all migrations are applied
- Verify Role enum exists in database
- Check admin middleware integration

### Development Tips

**1. Reset Admin Data**
```bash
# Reset database and recreate admin accounts
yarn p:m:r
yarn seed:admin:sample
```

**2. Check Admin Status**
```sql
-- Connect to database and check admin users
SELECT id, username, role, disabled FROM "User" WHERE role = 'ADMIN';
```

**3. Debug Authentication**
- Check browser cookies for auth token
- Verify JWT_SECRET matches between requests
- Review middleware logs

## Production Deployment

### Security Checklist
- [ ] Change all default passwords
- [ ] Use strong JWT_SECRET
- [ ] Enable HTTPS
- [ ] Configure secure cookie settings
- [ ] Set up proper CORS policies
- [ ] Enable rate limiting
- [ ] Configure audit log retention
- [ ] Set up monitoring alerts

### Environment Variables
```env
# Production settings
NODE_ENV=production
JWT_SECRET=<strong-random-secret>
ADMIN_DASHBOARD_PASSWORD=<strong-password>
SUPER_ADMIN_PASSWORD=<strong-password>

# Database
DATABASE_URL=<production-database-url>

# Security
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=<strong-random-secret>
```

### Monitoring Setup
- Configure error tracking (Sentry, etc.)
- Set up performance monitoring
- Enable audit log alerts
- Configure backup procedures

## Support

For issues or questions:
1. Check this documentation
2. Review error logs in admin dashboard
3. Check audit logs for security events
4. Verify environment configuration
5. Contact system administrator

---

**Last Updated**: December 2024
**Version**: 1.0.0
