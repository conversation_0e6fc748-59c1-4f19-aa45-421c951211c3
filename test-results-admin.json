{"config": {"configFile": "/Users/<USER>/Github/vocab/playwright.admin.config.ts", "rootDir": "/Users/<USER>/Github/vocab/e2e/admin", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Github/vocab/e2e/admin/global-setup.ts", "globalTeardown": "/Users/<USER>/Github/vocab/e2e/admin/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 4}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report-admin"}], ["json", {"outputFile": "test-results-admin.json"}], ["junit", {"outputFile": "test-results-admin.xml"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 4}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 4}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 4}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 4}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 4}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 4}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 4}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 4, "webServer": {"command": "yarn dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000, "env": {"NODE_ENV": "test"}}}, "suites": [{"title": "admin-auth.spec.ts", "file": "admin-auth.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Admin Au<PERSON>ntication", "file": "admin-auth.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should display admin login page correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 1737, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:50:33.368Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-e07b528e098b32590633", "file": "admin-auth.spec.ts", "line": 9, "column": 6}, {"title": "should login successfully with valid admin credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 5575, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:50:33.379Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-2f4faaa127380714a7fc", "file": "admin-auth.spec.ts", "line": 25, "column": 6}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 2449, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:50:33.371Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-faa3c86516706f823282", "file": "admin-auth.spec.ts", "line": 61, "column": 6}, {"title": "should show validation errors for empty fields", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 11858, "error": {"message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:87:5", "location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 5, "line": 87}, "snippet": "\u001b[0m \u001b[90m 85 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-sonner-toast]'\u001b[39m)\n \u001b[90m 86 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Please enter both username and password'\u001b[39m })\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 87 |\u001b[39m \t\t)\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m \t\t  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 88 |\u001b[39m \t})\u001b[33m;\u001b[39m\n \u001b[90m 89 |\u001b[39m\n \u001b[90m 90 |\u001b[39m \ttest(\u001b[32m'should toggle password visibility'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 5, "line": 87}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\u001b[22m\n\n\n\u001b[0m \u001b[90m 85 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-sonner-toast]'\u001b[39m)\n \u001b[90m 86 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Please enter both username and password'\u001b[39m })\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 87 |\u001b[39m \t\t)\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m \t\t  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 88 |\u001b[39m \t})\u001b[33m;\u001b[39m\n \u001b[90m 89 |\u001b[39m\n \u001b[90m 90 |\u001b[39m \ttest(\u001b[32m'should toggle password visibility'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:87:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:50:33.375Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 5, "line": 87}}], "status": "unexpected"}], "id": "4030f0bfef91ae700c6b-b5bc2839db4808544af5", "file": "admin-auth.spec.ts", "line": 78, "column": 6}, {"title": "should toggle password visibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 1541, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:50:35.340Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-ccd258b5471eba1ecec0", "file": "admin-auth.spec.ts", "line": 90, "column": 6}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 4674, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:50:36.044Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-4f07e2b40cbcda0e2c3a", "file": "admin-auth.spec.ts", "line": 110, "column": 6}, {"title": "should redirect to login when accessing protected route without auth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 2608, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:50:36.888Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-827a1f1084f083e154a5", "file": "admin-auth.spec.ts", "line": 126, "column": 6}, {"title": "should handle loading states during login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 2154, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:50:39.172Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-d5f440fcfc49ad1b2df0", "file": "admin-auth.spec.ts", "line": 137, "column": 6}, {"title": "should display admin login page correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 4, "parallelIndex": 0, "status": "passed", "duration": 3729, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:50:41.289Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-de90acda1fefc2217361", "file": "admin-auth.spec.ts", "line": 9, "column": 6}, {"title": "should login successfully with valid admin credentials", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 5, "parallelIndex": 2, "status": "failed", "duration": 20626, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:54:49", "location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 49, "line": 54}, "snippet": "\u001b[0m \u001b[90m 52 |\u001b[39m\n \u001b[90m 53 |\u001b[39m \t\t\u001b[90m// Should show either content or error state\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 54 |\u001b[39m \t\texpect(hasContent \u001b[33m||\u001b[39m hasError \u001b[33m||\u001b[39m hasTryAgain)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m \t\t                                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 55 |\u001b[39m\n \u001b[90m 56 |\u001b[39m \t\t\u001b[90m// Check navigation sidebar\u001b[39m\n \u001b[90m 57 |\u001b[39m \t\t\u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Admin Panel'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 49, "line": 54}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n\n\u001b[0m \u001b[90m 52 |\u001b[39m\n \u001b[90m 53 |\u001b[39m \t\t\u001b[90m// Should show either content or error state\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 54 |\u001b[39m \t\texpect(hasContent \u001b[33m||\u001b[39m hasError \u001b[33m||\u001b[39m hasTryAgain)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m \t\t                                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 55 |\u001b[39m\n \u001b[90m 56 |\u001b[39m \t\t\u001b[90m// Check navigation sidebar\u001b[39m\n \u001b[90m 57 |\u001b[39m \t\t\u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Admin Panel'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:54:49\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:50:42.287Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-firefox/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-firefox/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 49, "line": 54}}], "status": "unexpected"}], "id": "4030f0bfef91ae700c6b-d1cd827dff44d9751e85", "file": "admin-auth.spec.ts", "line": 25, "column": 6}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 6, "parallelIndex": 1, "status": "passed", "duration": 7966, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:50:42.536Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-da6b8ab7c64595c4e01d", "file": "admin-auth.spec.ts", "line": 61, "column": 6}, {"title": "should show validation errors for empty fields", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 7, "parallelIndex": 3, "status": "failed", "duration": 28103, "error": {"message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:87:5", "location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 5, "line": 87}, "snippet": "\u001b[0m \u001b[90m 85 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-sonner-toast]'\u001b[39m)\n \u001b[90m 86 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Please enter both username and password'\u001b[39m })\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 87 |\u001b[39m \t\t)\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m \t\t  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 88 |\u001b[39m \t})\u001b[33m;\u001b[39m\n \u001b[90m 89 |\u001b[39m\n \u001b[90m 90 |\u001b[39m \ttest(\u001b[32m'should toggle password visibility'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 5, "line": 87}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\u001b[22m\n\n\n\u001b[0m \u001b[90m 85 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-sonner-toast]'\u001b[39m)\n \u001b[90m 86 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Please enter both username and password'\u001b[39m })\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 87 |\u001b[39m \t\t)\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m \t\t  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 88 |\u001b[39m \t})\u001b[33m;\u001b[39m\n \u001b[90m 89 |\u001b[39m\n \u001b[90m 90 |\u001b[39m \ttest(\u001b[32m'should toggle password visibility'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:87:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:50:47.500Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-firefox/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-firefox/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 5, "line": 87}}], "status": "unexpected"}], "id": "4030f0bfef91ae700c6b-927e1d55fcab8d60e7ce", "file": "admin-auth.spec.ts", "line": 78, "column": 6}, {"title": "should toggle password visibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 4, "parallelIndex": 0, "status": "passed", "duration": 5475, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:50:47.491Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-a608d34dcc5c6a1eeba1", "file": "admin-auth.spec.ts", "line": 90, "column": 6}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 6, "parallelIndex": 1, "status": "passed", "duration": 16561, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:50:52.276Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-8a13156c56b46d2b43fd", "file": "admin-auth.spec.ts", "line": 110, "column": 6}, {"title": "should redirect to login when accessing protected route without auth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 4, "parallelIndex": 0, "status": "passed", "duration": 14884, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:50:52.978Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-0e8b4d0c5c71d74a4890", "file": "admin-auth.spec.ts", "line": 126, "column": 6}, {"title": "should handle loading states during login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 4, "parallelIndex": 0, "status": "passed", "duration": 10747, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:51:07.876Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-423b58a9bafab961e43f", "file": "admin-auth.spec.ts", "line": 137, "column": 6}, {"title": "should display admin login page correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 8, "parallelIndex": 2, "status": "passed", "duration": 11946, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:51:09.934Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-6c141e6d2620b9f23943", "file": "admin-auth.spec.ts", "line": 9, "column": 6}, {"title": "should login successfully with valid admin credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 9, "parallelIndex": 1, "status": "passed", "duration": 13222, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:51:11.219Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-9e0f96edac4fe93a188e", "file": "admin-auth.spec.ts", "line": 25, "column": 6}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 10, "parallelIndex": 0, "status": "passed", "duration": 10160, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:51:24.358Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-946aa1d11b1ac7da9b12", "file": "admin-auth.spec.ts", "line": 61, "column": 6}, {"title": "should show validation errors for empty fields", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 11, "parallelIndex": 3, "status": "failed", "duration": 15948, "error": {"message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:87:5", "location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 5, "line": 87}, "snippet": "\u001b[0m \u001b[90m 85 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-sonner-toast]'\u001b[39m)\n \u001b[90m 86 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Please enter both username and password'\u001b[39m })\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 87 |\u001b[39m \t\t)\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m \t\t  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 88 |\u001b[39m \t})\u001b[33m;\u001b[39m\n \u001b[90m 89 |\u001b[39m\n \u001b[90m 90 |\u001b[39m \ttest(\u001b[32m'should toggle password visibility'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 5, "line": 87}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\u001b[22m\n\n\n\u001b[0m \u001b[90m 85 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-sonner-toast]'\u001b[39m)\n \u001b[90m 86 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Please enter both username and password'\u001b[39m })\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 87 |\u001b[39m \t\t)\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m \t\t  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 88 |\u001b[39m \t})\u001b[33m;\u001b[39m\n \u001b[90m 89 |\u001b[39m\n \u001b[90m 90 |\u001b[39m \ttest(\u001b[32m'should toggle password visibility'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:87:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:51:24.355Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-webkit/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-webkit/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 5, "line": 87}}], "status": "unexpected"}], "id": "4030f0bfef91ae700c6b-92eff4f021ca8086fe26", "file": "admin-auth.spec.ts", "line": 78, "column": 6}, {"title": "should toggle password visibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 8, "parallelIndex": 2, "status": "passed", "duration": 1287, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:51:23.151Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-4bf2c60d2006eb42b18d", "file": "admin-auth.spec.ts", "line": 90, "column": 6}, {"title": "should logout successfully", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 8, "parallelIndex": 2, "status": "timedOut", "duration": 31886, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 14, "line": 115}, "message": "Error: page.waitForURL: Test timeout of 30000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"/admin\" until \"load\"\n============================================================\n\n\u001b[0m \u001b[90m 113 |\u001b[39m \t\t\u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[id=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'admin123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 114 |\u001b[39m \t\t\u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 115 |\u001b[39m \t\t\u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'/admin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m \t\t           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 116 |\u001b[39m\n \u001b[90m 117 |\u001b[39m \t\t\u001b[90m// Find and click logout button\u001b[39m\n \u001b[90m 118 |\u001b[39m \t\t\u001b[36mconst\u001b[39m logoutButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Logout'\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:115:14\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:51:24.445Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-webkit/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-webkit/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-webkit/error-context.md"}]}], "status": "unexpected"}], "id": "4030f0bfef91ae700c6b-5f8b7254028e1cc22a0d", "file": "admin-auth.spec.ts", "line": 110, "column": 6}, {"title": "should redirect to login when accessing protected route without auth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 9, "parallelIndex": 1, "status": "passed", "duration": 9534, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:51:25.718Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-36b3214bfb3a96d7efa3", "file": "admin-auth.spec.ts", "line": 126, "column": 6}, {"title": "should handle loading states during login", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 10, "parallelIndex": 0, "status": "timedOut", "duration": 30393, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 14, "line": 151}, "message": "Error: page.waitForURL: Test timeout of 30000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"/admin\" until \"load\"\n============================================================\n\n\u001b[0m \u001b[90m 149 |\u001b[39m\n \u001b[90m 150 |\u001b[39m \t\t\u001b[90m// Wait for successful navigation\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 151 |\u001b[39m \t\t\u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'/admin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m \t\t           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 152 |\u001b[39m \t})\u001b[33m;\u001b[39m\n \u001b[90m 153 |\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 154 |\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:151:14\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:51:34.965Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-1b10f-loading-states-during-login-webkit/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-1b10f-loading-states-during-login-webkit/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-1b10f-loading-states-during-login-webkit/error-context.md"}]}], "status": "unexpected"}], "id": "4030f0bfef91ae700c6b-cf1c1ae8c58245773f6d", "file": "admin-auth.spec.ts", "line": 137, "column": 6}, {"title": "should display admin login page correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 12, "parallelIndex": 1, "status": "passed", "duration": 2252, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:51:37.376Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-de3da8db9b428459a00e", "file": "admin-auth.spec.ts", "line": 9, "column": 6}, {"title": "should login successfully with valid admin credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 12, "parallelIndex": 1, "status": "passed", "duration": 5633, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:51:40.097Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-ee6050b5b4dae47fe573", "file": "admin-auth.spec.ts", "line": 25, "column": 6}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 13, "parallelIndex": 3, "status": "passed", "duration": 3247, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:51:44.127Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-f54ead5b5bbe9aed4077", "file": "admin-auth.spec.ts", "line": 61, "column": 6}, {"title": "should show validation errors for empty fields", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 12, "parallelIndex": 1, "status": "failed", "duration": 12297, "error": {"message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:87:5", "location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 5, "line": 87}, "snippet": "\u001b[0m \u001b[90m 85 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-sonner-toast]'\u001b[39m)\n \u001b[90m 86 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Please enter both username and password'\u001b[39m })\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 87 |\u001b[39m \t\t)\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m \t\t  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 88 |\u001b[39m \t})\u001b[33m;\u001b[39m\n \u001b[90m 89 |\u001b[39m\n \u001b[90m 90 |\u001b[39m \ttest(\u001b[32m'should toggle password visibility'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 5, "line": 87}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\u001b[22m\n\n\n\u001b[0m \u001b[90m 85 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-sonner-toast]'\u001b[39m)\n \u001b[90m 86 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Please enter both username and password'\u001b[39m })\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 87 |\u001b[39m \t\t)\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m \t\t  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 88 |\u001b[39m \t})\u001b[33m;\u001b[39m\n \u001b[90m 89 |\u001b[39m\n \u001b[90m 90 |\u001b[39m \ttest(\u001b[32m'should toggle password visibility'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:87:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:51:45.753Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Mobile-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Mobile-Chrome/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 5, "line": 87}}], "status": "unexpected"}], "id": "4030f0bfef91ae700c6b-347db03dd4cb5c642fa5", "file": "admin-auth.spec.ts", "line": 78, "column": 6}, {"title": "should toggle password visibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 13, "parallelIndex": 3, "status": "passed", "duration": 1857, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:51:47.894Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-c1b055c52f721ff9755b", "file": "admin-auth.spec.ts", "line": 90, "column": 6}, {"title": "should logout successfully", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 13, "parallelIndex": 3, "status": "failed", "duration": 14670, "error": {"message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button').filter({ hasText: 'Logout' })\u001b[22m\n\u001b[2m    - locator resolved to <button id=\"_r_8_\" tabindex=\"0\" role=\"button\" data-slot=\"button\" aria-disabled=\"false\" class=\"inline-flex items-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:bo…>…</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - element is outside of the viewport\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - element is outside of the viewport\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m    18 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - element is outside of the viewport\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n", "stack": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button').filter({ hasText: 'Logout' })\u001b[22m\n\u001b[2m    - locator resolved to <button id=\"_r_8_\" tabindex=\"0\" role=\"button\" data-slot=\"button\" aria-disabled=\"false\" class=\"inline-flex items-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:bo…>…</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - element is outside of the viewport\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - element is outside of the viewport\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m    18 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - element is outside of the viewport\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:119:22", "location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 22, "line": 119}, "snippet": "\u001b[0m \u001b[90m 117 |\u001b[39m \t\t\u001b[90m// Find and click logout button\u001b[39m\n \u001b[90m 118 |\u001b[39m \t\t\u001b[36mconst\u001b[39m logoutButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Logout'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 119 |\u001b[39m \t\t\u001b[36mawait\u001b[39m logoutButton\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m \t\t                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 120 |\u001b[39m\n \u001b[90m 121 |\u001b[39m \t\t\u001b[90m// Should redirect to login page\u001b[39m\n \u001b[90m 122 |\u001b[39m \t\t\u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'/admin/login'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 22, "line": 119}, "message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button').filter({ hasText: 'Logout' })\u001b[22m\n\u001b[2m    - locator resolved to <button id=\"_r_8_\" tabindex=\"0\" role=\"button\" data-slot=\"button\" aria-disabled=\"false\" class=\"inline-flex items-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:bo…>…</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - element is outside of the viewport\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - element is outside of the viewport\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m    18 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - element is outside of the viewport\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\n\n\u001b[0m \u001b[90m 117 |\u001b[39m \t\t\u001b[90m// Find and click logout button\u001b[39m\n \u001b[90m 118 |\u001b[39m \t\t\u001b[36mconst\u001b[39m logoutButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Logout'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 119 |\u001b[39m \t\t\u001b[36mawait\u001b[39m logoutButton\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m \t\t                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 120 |\u001b[39m\n \u001b[90m 121 |\u001b[39m \t\t\u001b[90m// Should redirect to login page\u001b[39m\n \u001b[90m 122 |\u001b[39m \t\t\u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'/admin/login'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:119:22\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:51:49.764Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Chrome/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 22, "line": 119}}], "status": "unexpected"}], "id": "4030f0bfef91ae700c6b-9693ee61f298a0c69fdc", "file": "admin-auth.spec.ts", "line": 110, "column": 6}, {"title": "should redirect to login when accessing protected route without auth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 14, "parallelIndex": 2, "status": "passed", "duration": 2133, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:51:58.074Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-de79b0a7bd57e65ee203", "file": "admin-auth.spec.ts", "line": 126, "column": 6}, {"title": "should handle loading states during login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 15, "parallelIndex": 1, "status": "passed", "duration": 3440, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:51:59.142Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-071f0a98c1e0d14e44b8", "file": "admin-auth.spec.ts", "line": 137, "column": 6}, {"title": "should display admin login page correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 16, "parallelIndex": 2, "status": "passed", "duration": 1623, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:52:01.778Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-30c42275ce7aceeacbdf", "file": "admin-auth.spec.ts", "line": 9, "column": 6}, {"title": "should login successfully with valid admin credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 17, "parallelIndex": 1, "status": "passed", "duration": 6137, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:52:04.014Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-8d5318867d4f182248c8", "file": "admin-auth.spec.ts", "line": 25, "column": 6}, {"title": "should show error for invalid credentials", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 16, "parallelIndex": 2, "status": "failed", "duration": 11760, "error": {"message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-sonner-toast]').filter({ hasText: 'Invalid username or password' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Invalid username or password' })\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-sonner-toast]').filter({ hasText: 'Invalid username or password' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Invalid username or password' })\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:72:5", "location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 5, "line": 72}, "snippet": "\u001b[0m \u001b[90m 70 |\u001b[39m \t\t\u001b[36mawait\u001b[39m expect(\n \u001b[90m 71 |\u001b[39m \t\t\tpage\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-sonner-toast]'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Invalid username or password'\u001b[39m })\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 72 |\u001b[39m \t\t)\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m \t\t  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 73 |\u001b[39m\n \u001b[90m 74 |\u001b[39m \t\t\u001b[90m// Should still be on login page\u001b[39m\n \u001b[90m 75 |\u001b[39m \t\t\u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[32m'/admin/login'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 5, "line": 72}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-sonner-toast]').filter({ hasText: 'Invalid username or password' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Invalid username or password' })\u001b[22m\n\n\n\u001b[0m \u001b[90m 70 |\u001b[39m \t\t\u001b[36mawait\u001b[39m expect(\n \u001b[90m 71 |\u001b[39m \t\t\tpage\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-sonner-toast]'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Invalid username or password'\u001b[39m })\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 72 |\u001b[39m \t\t)\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m \t\t  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 73 |\u001b[39m\n \u001b[90m 74 |\u001b[39m \t\t\u001b[90m// Should still be on login page\u001b[39m\n \u001b[90m 75 |\u001b[39m \t\t\u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[32m'/admin/login'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:72:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:52:04.300Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-224fa-ror-for-invalid-credentials-Mobile-Safari/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-224fa-ror-for-invalid-credentials-Mobile-Safari/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-224fa-ror-for-invalid-credentials-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 5, "line": 72}}], "status": "unexpected"}], "id": "4030f0bfef91ae700c6b-859929da8ed40aec96c2", "file": "admin-auth.spec.ts", "line": 61, "column": 6}, {"title": "should show validation errors for empty fields", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 18, "parallelIndex": 3, "status": "failed", "duration": 12326, "error": {"message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:87:5", "location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 5, "line": 87}, "snippet": "\u001b[0m \u001b[90m 85 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-sonner-toast]'\u001b[39m)\n \u001b[90m 86 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Please enter both username and password'\u001b[39m })\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 87 |\u001b[39m \t\t)\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m \t\t  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 88 |\u001b[39m \t})\u001b[33m;\u001b[39m\n \u001b[90m 89 |\u001b[39m\n \u001b[90m 90 |\u001b[39m \ttest(\u001b[32m'should toggle password visibility'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 5, "line": 87}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\u001b[22m\n\n\n\u001b[0m \u001b[90m 85 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-sonner-toast]'\u001b[39m)\n \u001b[90m 86 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Please enter both username and password'\u001b[39m })\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 87 |\u001b[39m \t\t)\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m \t\t  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 88 |\u001b[39m \t})\u001b[33m;\u001b[39m\n \u001b[90m 89 |\u001b[39m\n \u001b[90m 90 |\u001b[39m \ttest(\u001b[32m'should toggle password visibility'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:87:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:52:06.058Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Mobile-Safari/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Mobile-Safari/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 5, "line": 87}}], "status": "unexpected"}], "id": "4030f0bfef91ae700c6b-da797515f605e39b1768", "file": "admin-auth.spec.ts", "line": 78, "column": 6}, {"title": "should toggle password visibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 19, "parallelIndex": 0, "status": "passed", "duration": 3007, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:52:07.296Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-73e6cf7764f00300f993", "file": "admin-auth.spec.ts", "line": 90, "column": 6}, {"title": "should logout successfully", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 19, "parallelIndex": 0, "status": "failed", "duration": 17809, "error": {"message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button').filter({ hasText: 'Logout' })\u001b[22m\n\u001b[2m    - locator resolved to <button id=\"_r_a_\" tabindex=\"0\" role=\"button\" data-slot=\"button\" aria-disabled=\"false\" class=\"inline-flex items-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:bo…>…</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - element is outside of the viewport\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - element is outside of the viewport\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m    17 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - element is outside of the viewport\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n", "stack": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button').filter({ hasText: 'Logout' })\u001b[22m\n\u001b[2m    - locator resolved to <button id=\"_r_a_\" tabindex=\"0\" role=\"button\" data-slot=\"button\" aria-disabled=\"false\" class=\"inline-flex items-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:bo…>…</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - element is outside of the viewport\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - element is outside of the viewport\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m    17 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - element is outside of the viewport\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:119:22", "location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 22, "line": 119}, "snippet": "\u001b[0m \u001b[90m 117 |\u001b[39m \t\t\u001b[90m// Find and click logout button\u001b[39m\n \u001b[90m 118 |\u001b[39m \t\t\u001b[36mconst\u001b[39m logoutButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Logout'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 119 |\u001b[39m \t\t\u001b[36mawait\u001b[39m logoutButton\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m \t\t                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 120 |\u001b[39m\n \u001b[90m 121 |\u001b[39m \t\t\u001b[90m// Should redirect to login page\u001b[39m\n \u001b[90m 122 |\u001b[39m \t\t\u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'/admin/login'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 22, "line": 119}, "message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button').filter({ hasText: 'Logout' })\u001b[22m\n\u001b[2m    - locator resolved to <button id=\"_r_a_\" tabindex=\"0\" role=\"button\" data-slot=\"button\" aria-disabled=\"false\" class=\"inline-flex items-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:bo…>…</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - element is outside of the viewport\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - element is outside of the viewport\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m    17 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - element is outside of the viewport\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\n\n\u001b[0m \u001b[90m 117 |\u001b[39m \t\t\u001b[90m// Find and click logout button\u001b[39m\n \u001b[90m 118 |\u001b[39m \t\t\u001b[36mconst\u001b[39m logoutButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Logout'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 119 |\u001b[39m \t\t\u001b[36mawait\u001b[39m logoutButton\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m \t\t                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 120 |\u001b[39m\n \u001b[90m 121 |\u001b[39m \t\t\u001b[90m// Should redirect to login page\u001b[39m\n \u001b[90m 122 |\u001b[39m \t\t\u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'/admin/login'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:119:22\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:52:10.695Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Safari/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Safari/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 22, "line": 119}}], "status": "unexpected"}], "id": "4030f0bfef91ae700c6b-48ae075c7ef68f005e0e", "file": "admin-auth.spec.ts", "line": 110, "column": 6}, {"title": "should redirect to login when accessing protected route without auth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 17, "parallelIndex": 1, "status": "passed", "duration": 6261, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:52:10.698Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-4eb8598866cefa871fe4", "file": "admin-auth.spec.ts", "line": 126, "column": 6}, {"title": "should handle loading states during login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 20, "parallelIndex": 2, "status": "passed", "duration": 3564, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:52:17.738Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-3ab414b93b2cd6be4e8f", "file": "admin-auth.spec.ts", "line": 137, "column": 6}, {"title": "should display admin login page correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 21, "parallelIndex": 1, "status": "passed", "duration": 4368, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:52:17.820Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-72c3b507879d2e78ad0b", "file": "admin-auth.spec.ts", "line": 9, "column": 6}, {"title": "should login successfully with valid admin credentials", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 22, "parallelIndex": 3, "status": "failed", "duration": 15531, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:54:49", "location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 49, "line": 54}, "snippet": "\u001b[0m \u001b[90m 52 |\u001b[39m\n \u001b[90m 53 |\u001b[39m \t\t\u001b[90m// Should show either content or error state\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 54 |\u001b[39m \t\texpect(hasContent \u001b[33m||\u001b[39m hasError \u001b[33m||\u001b[39m hasTryAgain)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m \t\t                                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 55 |\u001b[39m\n \u001b[90m 56 |\u001b[39m \t\t\u001b[90m// Check navigation sidebar\u001b[39m\n \u001b[90m 57 |\u001b[39m \t\t\u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Admin Panel'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 49, "line": 54}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n\n\u001b[0m \u001b[90m 52 |\u001b[39m\n \u001b[90m 53 |\u001b[39m \t\t\u001b[90m// Should show either content or error state\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 54 |\u001b[39m \t\texpect(hasContent \u001b[33m||\u001b[39m hasError \u001b[33m||\u001b[39m hasTryAgain)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m \t\t                                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 55 |\u001b[39m\n \u001b[90m 56 |\u001b[39m \t\t\u001b[90m// Check navigation sidebar\u001b[39m\n \u001b[90m 57 |\u001b[39m \t\t\u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Admin Panel'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:54:49\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:52:21.525Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-Microsoft-Edge/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-Microsoft-Edge/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-Microsoft-Edge/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 49, "line": 54}}], "status": "unexpected"}], "id": "4030f0bfef91ae700c6b-5745c84357d881b33ece", "file": "admin-auth.spec.ts", "line": 25, "column": 6}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 23, "parallelIndex": 2, "status": "passed", "duration": 12305, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:52:25.372Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-83efad3e06fe673a39d4", "file": "admin-auth.spec.ts", "line": 61, "column": 6}, {"title": "should show validation errors for empty fields", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 21, "parallelIndex": 1, "status": "failed", "duration": 17153, "error": {"message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:87:5", "location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 5, "line": 87}, "snippet": "\u001b[0m \u001b[90m 85 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-sonner-toast]'\u001b[39m)\n \u001b[90m 86 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Please enter both username and password'\u001b[39m })\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 87 |\u001b[39m \t\t)\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m \t\t  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 88 |\u001b[39m \t})\u001b[33m;\u001b[39m\n \u001b[90m 89 |\u001b[39m\n \u001b[90m 90 |\u001b[39m \ttest(\u001b[32m'should toggle password visibility'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 5, "line": 87}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\u001b[22m\n\n\n\u001b[0m \u001b[90m 85 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-sonner-toast]'\u001b[39m)\n \u001b[90m 86 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Please enter both username and password'\u001b[39m })\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 87 |\u001b[39m \t\t)\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m \t\t  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 88 |\u001b[39m \t})\u001b[33m;\u001b[39m\n \u001b[90m 89 |\u001b[39m\n \u001b[90m 90 |\u001b[39m \ttest(\u001b[32m'should toggle password visibility'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:87:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:52:24.146Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Microsoft-Edge/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Microsoft-Edge/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Microsoft-Edge/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 5, "line": 87}}], "status": "unexpected"}], "id": "4030f0bfef91ae700c6b-0a4064ec9bb8fec4fe35", "file": "admin-auth.spec.ts", "line": 78, "column": 6}, {"title": "should toggle password visibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 24, "parallelIndex": 0, "status": "passed", "duration": 7015, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:52:35.820Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-fe163bd22b613efc5565", "file": "admin-auth.spec.ts", "line": 90, "column": 6}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 23, "parallelIndex": 2, "status": "passed", "duration": 5572, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:52:39.121Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-48e309fbb1066f5351ed", "file": "admin-auth.spec.ts", "line": 110, "column": 6}, {"title": "should redirect to login when accessing protected route without auth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 23, "parallelIndex": 2, "status": "passed", "duration": 3001, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:52:44.715Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-991373299bbbd2ab38e8", "file": "admin-auth.spec.ts", "line": 126, "column": 6}, {"title": "should handle loading states during login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 24, "parallelIndex": 0, "status": "passed", "duration": 3533, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:52:44.761Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-4f2ee0eb70d6d5d59a2a", "file": "admin-auth.spec.ts", "line": 137, "column": 6}, {"title": "should display admin login page correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 28, "parallelIndex": 2, "status": "passed", "duration": 4095, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:52:59.800Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-5c5b86a93858bff814b6", "file": "admin-auth.spec.ts", "line": 9, "column": 6}, {"title": "should login successfully with valid admin credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 27, "parallelIndex": 0, "status": "passed", "duration": 6203, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:52:55.590Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-0628ad6ce4554ab37ea8", "file": "admin-auth.spec.ts", "line": 25, "column": 6}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 25, "parallelIndex": 3, "status": "passed", "duration": 1394, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:52:49.878Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-29106df1bbb1b1dabcd8", "file": "admin-auth.spec.ts", "line": 61, "column": 6}, {"title": "should show validation errors for empty fields", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 25, "parallelIndex": 3, "status": "failed", "duration": 10778, "error": {"message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:87:5", "location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 5, "line": 87}, "snippet": "\u001b[0m \u001b[90m 85 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-sonner-toast]'\u001b[39m)\n \u001b[90m 86 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Please enter both username and password'\u001b[39m })\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 87 |\u001b[39m \t\t)\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m \t\t  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 88 |\u001b[39m \t})\u001b[33m;\u001b[39m\n \u001b[90m 89 |\u001b[39m\n \u001b[90m 90 |\u001b[39m \ttest(\u001b[32m'should toggle password visibility'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 5, "line": 87}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-sonner-toast]').filter({ hasText: 'Please enter both username and password' })\u001b[22m\n\n\n\u001b[0m \u001b[90m 85 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-sonner-toast]'\u001b[39m)\n \u001b[90m 86 |\u001b[39m \t\t\t\t\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Please enter both username and password'\u001b[39m })\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 87 |\u001b[39m \t\t)\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m \t\t  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 88 |\u001b[39m \t})\u001b[33m;\u001b[39m\n \u001b[90m 89 |\u001b[39m\n \u001b[90m 90 |\u001b[39m \ttest(\u001b[32m'should toggle password visibility'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:87:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:52:51.662Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Google-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Google-Chrome/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-77aa5-ion-errors-for-empty-fields-Google-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 5, "line": 87}}], "status": "unexpected"}], "id": "4030f0bfef91ae700c6b-fe558859a06439fe946e", "file": "admin-auth.spec.ts", "line": 78, "column": 6}, {"title": "should toggle password visibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 26, "parallelIndex": 1, "status": "passed", "duration": 1104, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:52:53.749Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-6e8be48d9fd5d4d3b34e", "file": "admin-auth.spec.ts", "line": 90, "column": 6}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 26, "parallelIndex": 1, "status": "passed", "duration": 3802, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:52:55.186Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-085817e09d2cc5767a8a", "file": "admin-auth.spec.ts", "line": 110, "column": 6}, {"title": "should redirect to login when accessing protected route without auth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 26, "parallelIndex": 1, "status": "passed", "duration": 3258, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:52:59.005Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-ccc7711631484b066f5b", "file": "admin-auth.spec.ts", "line": 126, "column": 6}, {"title": "should handle loading states during login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 26, "parallelIndex": 1, "status": "passed", "duration": 1525, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T15:53:02.279Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-5ab003898c15cef14490", "file": "admin-auth.spec.ts", "line": 137, "column": 6}]}]}], "errors": [], "stats": {"startTime": "2025-07-21T15:50:19.867Z", "duration": 171868.874, "expected": 42, "skipped": 0, "unexpected": 14, "flaky": 0}}