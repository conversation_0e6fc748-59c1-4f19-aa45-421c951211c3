{"config": {"configFile": "/Users/<USER>/Github/vocab/playwright.admin.config.ts", "rootDir": "/Users/<USER>/Github/vocab/e2e/admin", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Github/vocab/e2e/admin/global-setup.ts", "globalTeardown": "/Users/<USER>/Github/vocab/e2e/admin/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report-admin"}], ["json", {"outputFile": "test-results-admin.json"}], ["junit", {"outputFile": "test-results-admin.xml"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 1}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 1}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 1}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 1}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 1}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 1}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 4, "webServer": {"command": "yarn dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000, "env": {"NODE_ENV": "test"}}}, "suites": [{"title": "admin-auth.spec.ts", "file": "admin-auth.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Admin Au<PERSON>ntication", "file": "admin-auth.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should login successfully with valid admin credentials", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 1219, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:48:49", "location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 49, "line": 48}, "snippet": "\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m \t\t\u001b[90m// Should show either content or error state\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m \t\texpect(hasContent \u001b[33m||\u001b[39m hasError \u001b[33m||\u001b[39m hasTryAgain)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m \t\t                                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m\n \u001b[90m 50 |\u001b[39m \t\t\u001b[90m// Check navigation sidebar\u001b[39m\n \u001b[90m 51 |\u001b[39m \t\t\u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Admin Panel'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 49, "line": 48}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m \t\t\u001b[90m// Should show either content or error state\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m \t\texpect(hasContent \u001b[33m||\u001b[39m hasError \u001b[33m||\u001b[39m hasTryAgain)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m \t\t                                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m\n \u001b[90m 50 |\u001b[39m \t\t\u001b[90m// Check navigation sidebar\u001b[39m\n \u001b[90m 51 |\u001b[39m \t\t\u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Admin Panel'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:48:49\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T14:34:22.360Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 49, "line": 48}}], "status": "unexpected"}], "id": "4030f0bfef91ae700c6b-2f4faaa127380714a7fc", "file": "admin-auth.spec.ts", "line": 25, "column": 6}]}]}], "errors": [], "stats": {"startTime": "2025-07-21T14:34:10.507Z", "duration": 13237.238, "expected": 0, "skipped": 0, "unexpected": 1, "flaky": 0}}