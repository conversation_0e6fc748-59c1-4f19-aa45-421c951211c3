{"config": {"configFile": "/Users/<USER>/Github/vocab/playwright.admin.config.ts", "rootDir": "/Users/<USER>/Github/vocab/e2e/admin", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Github/vocab/e2e/admin/global-setup.ts", "globalTeardown": "/Users/<USER>/Github/vocab/e2e/admin/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests"}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report-admin"}], ["json", {"outputFile": "test-results-admin.json"}], ["junit", {"outputFile": "test-results-admin.xml"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests"}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests"}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests"}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests"}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests"}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests"}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests"}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 4, "webServer": {"command": "yarn dev", "url": "http://localhost:3002", "reuseExistingServer": true, "timeout": 120000, "env": {"NODE_ENV": "test"}}}, "suites": [], "errors": [{"message": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/admin\" until \"load\"\n============================================================", "stack": "TimeoutError: page.waitForURL: Timeout 10000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"**/admin\" until \"load\"\n============================================================\n    at globalSetup (/Users/<USER>/Github/vocab/e2e/admin/global-setup.ts:64:15)", "location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/global-setup.ts", "column": 15, "line": 64}, "snippet": "\u001b[90m   at \u001b[39mglobal-setup.ts:64\n\n\u001b[0m \u001b[90m 62 |\u001b[39m\n \u001b[90m 63 |\u001b[39m \t\t\t\u001b[90m// Wait for redirect to admin dashboard\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 64 |\u001b[39m \t\t\t\u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'**/admin'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m \t\t\t           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 65 |\u001b[39m\n \u001b[90m 66 |\u001b[39m \t\t\t\u001b[90m// Verify dashboard loads\u001b[39m\n \u001b[90m 67 |\u001b[39m \t\t\t\u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'text=Admin Dashboard'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}], "stats": {"startTime": "2025-07-21T14:22:11.110Z", "duration": 22199.726, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}