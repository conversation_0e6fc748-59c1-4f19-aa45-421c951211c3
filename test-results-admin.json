{"config": {"configFile": "/Users/<USER>/Github/vocab/playwright.admin.config.ts", "rootDir": "/Users/<USER>/Github/vocab/e2e/admin", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Github/vocab/e2e/admin/global-setup.ts", "globalTeardown": "/Users/<USER>/Github/vocab/e2e/admin/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 4}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report-admin"}], ["json", {"outputFile": "test-results-admin.json"}], ["junit", {"outputFile": "test-results-admin.xml"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 4}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 4}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 4}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 4}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 4}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 4}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 4}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 4, "webServer": {"command": "yarn dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000, "env": {"NODE_ENV": "test"}}}, "suites": [{"title": "admin-auth.spec.ts", "file": "admin-auth.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Admin Au<PERSON>ntication", "file": "admin-auth.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should display admin login page correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 1801, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:03:47.725Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-e07b528e098b32590633", "file": "admin-auth.spec.ts", "line": 9, "column": 6}, {"title": "should login successfully with valid admin credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 5610, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:03:47.667Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-2f4faaa127380714a7fc", "file": "admin-auth.spec.ts", "line": 25, "column": 6}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 2848, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:03:47.674Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-faa3c86516706f823282", "file": "admin-auth.spec.ts", "line": 61, "column": 6}, {"title": "should show validation errors for empty fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 1784, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:03:47.694Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-b5bc2839db4808544af5", "file": "admin-auth.spec.ts", "line": 78, "column": 6}, {"title": "should toggle password visibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 1683, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:03:49.681Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-ccd258b5471eba1ecec0", "file": "admin-auth.spec.ts", "line": 90, "column": 6}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 5440, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:03:49.699Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-4f07e2b40cbcda0e2c3a", "file": "admin-auth.spec.ts", "line": 110, "column": 6}, {"title": "should redirect to login when accessing protected route without auth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 3567, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:03:50.739Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-827a1f1084f083e154a5", "file": "admin-auth.spec.ts", "line": 126, "column": 6}, {"title": "should handle loading states during login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 3772, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:03:51.372Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-d5f440fcfc49ad1b2df0", "file": "admin-auth.spec.ts", "line": 137, "column": 6}, {"title": "should display admin login page correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 4, "parallelIndex": 1, "status": "passed", "duration": 2244, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:03:54.855Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-de90acda1fefc2217361", "file": "admin-auth.spec.ts", "line": 9, "column": 6}, {"title": "should login successfully with valid admin credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 5, "parallelIndex": 2, "status": "passed", "duration": 8893, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:03:55.722Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-d1cd827dff44d9751e85", "file": "admin-auth.spec.ts", "line": 25, "column": 6}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 6, "parallelIndex": 3, "status": "passed", "duration": 11212, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:03:56.066Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-da6b8ab7c64595c4e01d", "file": "admin-auth.spec.ts", "line": 61, "column": 6}, {"title": "should show validation errors for empty fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 7, "parallelIndex": 0, "status": "passed", "duration": 6619, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:03:56.103Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-927e1d55fcab8d60e7ce", "file": "admin-auth.spec.ts", "line": 78, "column": 6}, {"title": "should toggle password visibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 4, "parallelIndex": 1, "status": "passed", "duration": 4083, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:03:58.694Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-a608d34dcc5c6a1eeba1", "file": "admin-auth.spec.ts", "line": 90, "column": 6}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 4, "parallelIndex": 1, "status": "passed", "duration": 10384, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:02.832Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-8a13156c56b46d2b43fd", "file": "admin-auth.spec.ts", "line": 110, "column": 6}, {"title": "should redirect to login when accessing protected route without auth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 7, "parallelIndex": 0, "status": "passed", "duration": 7796, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:04.430Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-0e8b4d0c5c71d74a4890", "file": "admin-auth.spec.ts", "line": 126, "column": 6}, {"title": "should handle loading states during login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 5, "parallelIndex": 2, "status": "passed", "duration": 6994, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:05.751Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-423b58a9bafab961e43f", "file": "admin-auth.spec.ts", "line": 137, "column": 6}, {"title": "should display admin login page correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 8, "parallelIndex": 3, "status": "passed", "duration": 3387, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:13.599Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-6c141e6d2620b9f23943", "file": "admin-auth.spec.ts", "line": 9, "column": 6}, {"title": "should login successfully with valid admin credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 9, "parallelIndex": 0, "status": "passed", "duration": 7896, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:13.859Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-9e0f96edac4fe93a188e", "file": "admin-auth.spec.ts", "line": 25, "column": 6}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 10, "parallelIndex": 2, "status": "passed", "duration": 3672, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:14.463Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-946aa1d11b1ac7da9b12", "file": "admin-auth.spec.ts", "line": 61, "column": 6}, {"title": "should show validation errors for empty fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 11, "parallelIndex": 1, "status": "passed", "duration": 3629, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:14.779Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-92eff4f021ca8086fe26", "file": "admin-auth.spec.ts", "line": 78, "column": 6}, {"title": "should toggle password visibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 8, "parallelIndex": 3, "status": "passed", "duration": 3622, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:17.395Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-4bf2c60d2006eb42b18d", "file": "admin-auth.spec.ts", "line": 90, "column": 6}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 10, "parallelIndex": 2, "status": "passed", "duration": 5913, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:18.397Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-5f8b7254028e1cc22a0d", "file": "admin-auth.spec.ts", "line": 110, "column": 6}, {"title": "should redirect to login when accessing protected route without auth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 11, "parallelIndex": 1, "status": "passed", "duration": 4385, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:19.017Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-36b3214bfb3a96d7efa3", "file": "admin-auth.spec.ts", "line": 126, "column": 6}, {"title": "should handle loading states during login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 8, "parallelIndex": 3, "status": "passed", "duration": 3284, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:21.032Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-cf1c1ae8c58245773f6d", "file": "admin-auth.spec.ts", "line": 137, "column": 6}, {"title": "should display admin login page correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 12, "parallelIndex": 0, "status": "passed", "duration": 1363, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:24.022Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-de3da8db9b428459a00e", "file": "admin-auth.spec.ts", "line": 9, "column": 6}, {"title": "should login successfully with valid admin credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 13, "parallelIndex": 1, "status": "passed", "duration": 6364, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:25.053Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-ee6050b5b4dae47fe573", "file": "admin-auth.spec.ts", "line": 25, "column": 6}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 14, "parallelIndex": 2, "status": "passed", "duration": 3696, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:25.643Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-f54ead5b5bbe9aed4077", "file": "admin-auth.spec.ts", "line": 61, "column": 6}, {"title": "should show validation errors for empty fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 15, "parallelIndex": 3, "status": "passed", "duration": 2982, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:25.895Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-347db03dd4cb5c642fa5", "file": "admin-auth.spec.ts", "line": 78, "column": 6}, {"title": "should toggle password visibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 12, "parallelIndex": 0, "status": "passed", "duration": 2355, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:25.907Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-c1b055c52f721ff9755b", "file": "admin-auth.spec.ts", "line": 90, "column": 6}, {"title": "should logout successfully", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 12, "parallelIndex": 0, "status": "failed", "duration": 13424, "error": {"message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button').filter({ hasText: 'Logout' })\u001b[22m\n\u001b[2m    - locator resolved to <button id=\"_r_10_\" tabindex=\"0\" role=\"button\" data-slot=\"button\" aria-disabled=\"false\" class=\"inline-flex items-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:b…>…</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - element is outside of the viewport\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - element is outside of the viewport\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m    18 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - element is outside of the viewport\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n", "stack": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button').filter({ hasText: 'Logout' })\u001b[22m\n\u001b[2m    - locator resolved to <button id=\"_r_10_\" tabindex=\"0\" role=\"button\" data-slot=\"button\" aria-disabled=\"false\" class=\"inline-flex items-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:b…>…</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - element is outside of the viewport\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - element is outside of the viewport\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m    18 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - element is outside of the viewport\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:119:22", "location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 22, "line": 119}, "snippet": "\u001b[0m \u001b[90m 117 |\u001b[39m \t\t\u001b[90m// Find and click logout button\u001b[39m\n \u001b[90m 118 |\u001b[39m \t\t\u001b[36mconst\u001b[39m logoutButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Logout'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 119 |\u001b[39m \t\t\u001b[36mawait\u001b[39m logoutButton\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m \t\t                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 120 |\u001b[39m\n \u001b[90m 121 |\u001b[39m \t\t\u001b[90m// Should redirect to login page\u001b[39m\n \u001b[90m 122 |\u001b[39m \t\t\u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'/admin/login'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 22, "line": 119}, "message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button').filter({ hasText: 'Logout' })\u001b[22m\n\u001b[2m    - locator resolved to <button id=\"_r_10_\" tabindex=\"0\" role=\"button\" data-slot=\"button\" aria-disabled=\"false\" class=\"inline-flex items-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:b…>…</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - element is outside of the viewport\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - element is outside of the viewport\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m    18 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - element is outside of the viewport\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\n\n\u001b[0m \u001b[90m 117 |\u001b[39m \t\t\u001b[90m// Find and click logout button\u001b[39m\n \u001b[90m 118 |\u001b[39m \t\t\u001b[36mconst\u001b[39m logoutButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Logout'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 119 |\u001b[39m \t\t\u001b[36mawait\u001b[39m logoutButton\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m \t\t                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 120 |\u001b[39m\n \u001b[90m 121 |\u001b[39m \t\t\u001b[90m// Should redirect to login page\u001b[39m\n \u001b[90m 122 |\u001b[39m \t\t\u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'/admin/login'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:119:22\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:28.269Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Chrome/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 22, "line": 119}}], "status": "unexpected"}], "id": "4030f0bfef91ae700c6b-9693ee61f298a0c69fdc", "file": "admin-auth.spec.ts", "line": 110, "column": 6}, {"title": "should redirect to login when accessing protected route without auth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 15, "parallelIndex": 3, "status": "passed", "duration": 2807, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:29.301Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-de79b0a7bd57e65ee203", "file": "admin-auth.spec.ts", "line": 126, "column": 6}, {"title": "should handle loading states during login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 14, "parallelIndex": 2, "status": "passed", "duration": 2867, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:29.818Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-071f0a98c1e0d14e44b8", "file": "admin-auth.spec.ts", "line": 137, "column": 6}, {"title": "should display admin login page correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 16, "parallelIndex": 1, "status": "passed", "duration": 1881, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:32.681Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-30c42275ce7aceeacbdf", "file": "admin-auth.spec.ts", "line": 9, "column": 6}, {"title": "should login successfully with valid admin credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 17, "parallelIndex": 3, "status": "passed", "duration": 5848, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:33.186Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-8d5318867d4f182248c8", "file": "admin-auth.spec.ts", "line": 25, "column": 6}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 18, "parallelIndex": 2, "status": "passed", "duration": 3387, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:33.500Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-859929da8ed40aec96c2", "file": "admin-auth.spec.ts", "line": 61, "column": 6}, {"title": "should show validation errors for empty fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 16, "parallelIndex": 1, "status": "passed", "duration": 2379, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:35.042Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-da797515f605e39b1768", "file": "admin-auth.spec.ts", "line": 78, "column": 6}, {"title": "should toggle password visibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 16, "parallelIndex": 1, "status": "passed", "duration": 1741, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:37.444Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-73e6cf7764f00300f993", "file": "admin-auth.spec.ts", "line": 90, "column": 6}, {"title": "should logout successfully", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 18, "parallelIndex": 2, "status": "failed", "duration": 13689, "error": {"message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button').filter({ hasText: 'Logout' })\u001b[22m\n\u001b[2m    - locator resolved to <button id=\"_r_a_\" tabindex=\"0\" role=\"button\" data-slot=\"button\" aria-disabled=\"false\" class=\"inline-flex items-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:bo…>…</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - element is outside of the viewport\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - element is outside of the viewport\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m    17 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - element is outside of the viewport\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n", "stack": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button').filter({ hasText: 'Logout' })\u001b[22m\n\u001b[2m    - locator resolved to <button id=\"_r_a_\" tabindex=\"0\" role=\"button\" data-slot=\"button\" aria-disabled=\"false\" class=\"inline-flex items-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:bo…>…</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - element is outside of the viewport\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - element is outside of the viewport\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m    17 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - element is outside of the viewport\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:119:22", "location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 22, "line": 119}, "snippet": "\u001b[0m \u001b[90m 117 |\u001b[39m \t\t\u001b[90m// Find and click logout button\u001b[39m\n \u001b[90m 118 |\u001b[39m \t\t\u001b[36mconst\u001b[39m logoutButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Logout'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 119 |\u001b[39m \t\t\u001b[36mawait\u001b[39m logoutButton\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m \t\t                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 120 |\u001b[39m\n \u001b[90m 121 |\u001b[39m \t\t\u001b[90m// Should redirect to login page\u001b[39m\n \u001b[90m 122 |\u001b[39m \t\t\u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'/admin/login'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 22, "line": 119}, "message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button').filter({ hasText: 'Logout' })\u001b[22m\n\u001b[2m    - locator resolved to <button id=\"_r_a_\" tabindex=\"0\" role=\"button\" data-slot=\"button\" aria-disabled=\"false\" class=\"inline-flex items-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:bo…>…</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - element is outside of the viewport\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - element is outside of the viewport\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m    17 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is visible, enabled and stable\u001b[22m\n\u001b[2m       - scrolling into view if needed\u001b[22m\n\u001b[2m       - done scrolling\u001b[22m\n\u001b[2m       - element is outside of the viewport\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\n\n\u001b[0m \u001b[90m 117 |\u001b[39m \t\t\u001b[90m// Find and click logout button\u001b[39m\n \u001b[90m 118 |\u001b[39m \t\t\u001b[36mconst\u001b[39m logoutButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button'\u001b[39m)\u001b[33m.\u001b[39mfilter({ hasText\u001b[33m:\u001b[39m \u001b[32m'Logout'\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 119 |\u001b[39m \t\t\u001b[36mawait\u001b[39m logoutButton\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m \t\t                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 120 |\u001b[39m\n \u001b[90m 121 |\u001b[39m \t\t\u001b[90m// Should redirect to login page\u001b[39m\n \u001b[90m 122 |\u001b[39m \t\t\u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'/admin/login'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:119:22\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:37.447Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Safari/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Safari/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 22, "line": 119}}], "status": "unexpected"}], "id": "4030f0bfef91ae700c6b-48ae075c7ef68f005e0e", "file": "admin-auth.spec.ts", "line": 110, "column": 6}, {"title": "should redirect to login when accessing protected route without auth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 16, "parallelIndex": 1, "status": "passed", "duration": 3192, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:39.198Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-4eb8598866cefa871fe4", "file": "admin-auth.spec.ts", "line": 126, "column": 6}, {"title": "should handle loading states during login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 17, "parallelIndex": 3, "status": "passed", "duration": 3209, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:39.307Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-3ab414b93b2cd6be4e8f", "file": "admin-auth.spec.ts", "line": 137, "column": 6}, {"title": "should display admin login page correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 19, "parallelIndex": 0, "status": "passed", "duration": 2461, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:43.137Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-72c3b507879d2e78ad0b", "file": "admin-auth.spec.ts", "line": 9, "column": 6}, {"title": "should login successfully with valid admin credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 20, "parallelIndex": 1, "status": "passed", "duration": 5904, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:43.591Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-5745c84357d881b33ece", "file": "admin-auth.spec.ts", "line": 25, "column": 6}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 21, "parallelIndex": 3, "status": "passed", "duration": 3248, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:43.743Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-83efad3e06fe673a39d4", "file": "admin-auth.spec.ts", "line": 61, "column": 6}, {"title": "should show validation errors for empty fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 19, "parallelIndex": 0, "status": "passed", "duration": 1921, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:47.133Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-0a4064ec9bb8fec4fe35", "file": "admin-auth.spec.ts", "line": 78, "column": 6}, {"title": "should toggle password visibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 21, "parallelIndex": 3, "status": "passed", "duration": 2137, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:47.840Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-fe163bd22b613efc5565", "file": "admin-auth.spec.ts", "line": 90, "column": 6}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 19, "parallelIndex": 0, "status": "passed", "duration": 6417, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:49.073Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-48e309fbb1066f5351ed", "file": "admin-auth.spec.ts", "line": 110, "column": 6}, {"title": "should redirect to login when accessing protected route without auth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 21, "parallelIndex": 3, "status": "passed", "duration": 4536, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:49.988Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-991373299bbbd2ab38e8", "file": "admin-auth.spec.ts", "line": 126, "column": 6}, {"title": "should handle loading states during login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 20, "parallelIndex": 1, "status": "passed", "duration": 5297, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:50.488Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-4f2ee0eb70d6d5d59a2a", "file": "admin-auth.spec.ts", "line": 137, "column": 6}, {"title": "should display admin login page correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 22, "parallelIndex": 2, "status": "passed", "duration": 1725, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:53.483Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-5c5b86a93858bff814b6", "file": "admin-auth.spec.ts", "line": 9, "column": 6}, {"title": "should login successfully with valid admin credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 23, "parallelIndex": 3, "status": "passed", "duration": 4524, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:56.692Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-0628ad6ce4554ab37ea8", "file": "admin-auth.spec.ts", "line": 25, "column": 6}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 24, "parallelIndex": 0, "status": "passed", "duration": 3065, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:06:45.702Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-29106df1bbb1b1dabcd8", "file": "admin-auth.spec.ts", "line": 61, "column": 6}, {"title": "should show validation errors for empty fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "4030f0bfef91ae700c6b-fe558859a06439fe946e", "file": "admin-auth.spec.ts", "line": 78, "column": 6}, {"title": "should toggle password visibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 22, "parallelIndex": 2, "status": "passed", "duration": 1442, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:56.631Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-6e8be48d9fd5d4d3b34e", "file": "admin-auth.spec.ts", "line": 90, "column": 6}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 22, "parallelIndex": 2, "status": "passed", "duration": 2940, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:04:58.097Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-085817e09d2cc5767a8a", "file": "admin-auth.spec.ts", "line": 110, "column": 6}, {"title": "should redirect to login when accessing protected route without auth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 22, "parallelIndex": 2, "status": "passed", "duration": 1158, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:05:01.092Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-ccc7711631484b066f5b", "file": "admin-auth.spec.ts", "line": 126, "column": 6}, {"title": "should handle loading states during login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 23, "parallelIndex": 3, "status": "passed", "duration": 1633, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:05:01.736Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-5ab003898c15cef14490", "file": "admin-auth.spec.ts", "line": 137, "column": 6}]}]}], "errors": [], "stats": {"startTime": "2025-07-21T16:03:34.079Z", "duration": 382908.87, "expected": 53, "skipped": 1, "unexpected": 2, "flaky": 0}}