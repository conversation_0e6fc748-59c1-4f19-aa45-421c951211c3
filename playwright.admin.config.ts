import { defineConfig, devices } from '@playwright/test';

/**
 * Admin Dashboard Test Configuration
 *
 * This configuration is specifically for testing the admin dashboard functionality.
 * It includes setup for admin authentication, database seeding, and comprehensive testing.
 */
export default defineConfig({
	// Test directory for admin tests
	testDir: './e2e/admin',

	// Run tests in files in parallel
	fullyParallel: true,

	// Fail the build on CI if you accidentally left test.only in the source code
	forbidOnly: !!process.env.CI,

	// Retry on CI only
	retries: process.env.CI ? 2 : 0,

	// Opt out of parallel tests on CI
	workers: process.env.CI ? 1 : undefined,

	// Reporter to use
	reporter: [
		['html', { outputFolder: 'playwright-report-admin' }],
		['json', { outputFile: 'test-results-admin.json' }],
		['junit', { outputFile: 'test-results-admin.xml' }],
		['list'],
	],

	// Shared settings for all the projects below
	use: {
		// Base URL to use in actions like `await page.goto('/')`
		baseURL: 'http://localhost:3002',

		// Collect trace when retrying the failed test
		trace: 'on-first-retry',

		// Take screenshot on failure
		screenshot: 'only-on-failure',

		// Record video on failure
		video: 'retain-on-failure',

		// Global timeout for each action
		actionTimeout: 10000,

		// Global timeout for navigation
		navigationTimeout: 30000,
	},

	// Configure projects for major browsers
	projects: [
		{
			name: 'chromium',
			use: { ...devices['Desktop Chrome'] },
		},

		{
			name: 'firefox',
			use: { ...devices['Desktop Firefox'] },
		},

		{
			name: 'webkit',
			use: { ...devices['Desktop Safari'] },
		},

		// Test against mobile viewports
		{
			name: 'Mobile Chrome',
			use: { ...devices['Pixel 5'] },
		},

		{
			name: 'Mobile Safari',
			use: { ...devices['iPhone 12'] },
		},

		// Test against branded browsers
		{
			name: 'Microsoft Edge',
			use: { ...devices['Desktop Edge'], channel: 'msedge' },
		},

		{
			name: 'Google Chrome',
			use: { ...devices['Desktop Chrome'], channel: 'chrome' },
		},
	],

	// Global setup and teardown
	globalSetup: require.resolve('./e2e/admin/global-setup.ts'),
	globalTeardown: require.resolve('./e2e/admin/global-teardown.ts'),

	// Run your local dev server before starting the tests
	webServer: {
		command: 'yarn dev',
		url: 'http://localhost:3002',
		reuseExistingServer: !process.env.CI,
		timeout: 120 * 1000, // 2 minutes
		env: {
			NODE_ENV: 'test',
		},
	},

	// Test timeout
	timeout: 30 * 1000, // 30 seconds

	// Expect timeout
	expect: {
		timeout: 5 * 1000, // 5 seconds
	},

	// Output directory for test artifacts
	outputDir: 'test-results-admin/',

	// Test metadata
	metadata: {
		testType: 'admin-dashboard',
		version: '1.0.0',
		description: 'Comprehensive admin dashboard functionality tests',
	},
});
